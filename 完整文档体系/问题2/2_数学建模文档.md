# 问题2：信号灯优化模型 - 数学建模文档

## 📐 **建模目标与问题分析**

### 建模目标
1. 建立基于交通流的信号灯优化数学模型
2. 构建四相位信号控制的约束优化模型
3. 最小化交叉口总延误时间
4. 建立多目标优化的信号配时模型

### 问题分解
- **决策变量**: 各相位绿灯时间分配
- **目标函数**: 最小化平均延误时间
- **约束条件**: 时间约束、相位冲突约束
- **优化方法**: 线性规划、非线性规划、启发式算法

## 🔢 **核心数学模型**

### 1. 基础定义与符号说明

#### 1.1 集合定义
P = {1, 2, 3, 4}           # 相位集合
D = {1, 2, 3, 4}           # 方向集合
M = {直行, 左转, 右转}      # 行驶方向集合

#### 1.2 参数定义
gᵢ     # 相位i的绿灯时间
C      # 信号周期时长
S      # 饱和流率

#### 1.3 决策变量
gᵢ ∈ [gₘᵢₙ, gₘₐₓ]  # 相位i的绿灯时间

### 2. 主要数学模型

#### 2.1 目标函数
min Z = Σᵢ₌₁⁴ qᵢ × dᵢ(gᵢ)

#### 2.2 约束条件
Σᵢ₌₁⁴ gᵢ ≤ C - L
gₘᵢₙ ≤ gᵢ ≤ gₘₐₓ

#### 2.3 模型特性分析
- 非线性目标函数
- 线性约束条件
- NP-hard问题

### 3. 求解方法

#### 3.1 解析求解
Webster最优周期公式：
C₀ = (1.5L + 5)/(1 - ΣYᵢ)

#### 3.2 数值求解
序列二次规划(SQP)方法

#### 3.3 启发式算法
遗传算法、模拟退火、粒子群优化

## 📊 **模型验证与评估**

### 1. 模型验证
- 仿真验证
- 现场测试
- 理论分析

### 2. 敏感性分析
参数敏感性分析

### 3. 稳定性分析
鲁棒性分析

## 🎯 **模型应用实例**

### 实例分析
5月2日第三时段信号优化实例

## 📋 **模型总结**

### 模型优势
- 理论基础完备
- 优化效果显著
- 实用性强

### 模型局限
- 假设简化
- 计算复杂度高

### 改进方向
- 实时自适应优化
- 多路口协调
- 机器学习方法

这个数学建模文档为问题2提供了完整的理论框架和数学基础。
