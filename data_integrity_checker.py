#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据完整性检查器
检查所有生成文件的完整性和正确性
"""

import os
import csv
import json
from collections import defaultdict

class DataIntegrityChecker:
    def __init__(self):
        """初始化完整性检查器"""
        self.csv_dir = 'csv_data'
        self.excel_dir = 'excel_data'
        self.results_dir = 'results'
        self.viz_dir = 'visualizations'
        
        self.check_results = {}
        
    def check_directory_structure(self):
        """检查目录结构"""
        print("="*60)
        print("1. 检查目录结构")
        print("="*60)
        
        required_dirs = [self.csv_dir, self.excel_dir, self.results_dir, self.viz_dir]
        
        for directory in required_dirs:
            if os.path.exists(directory):
                file_count = len(os.listdir(directory))
                print(f"✓ {directory}/ 存在 ({file_count} 个文件)")
            else:
                print(f"✗ {directory}/ 不存在")
        
        # 检查主要文件
        main_files = [
            '2024数模大赛E题论文.docx',
            '项目完成总结.md',
            '附件2.csv'
        ]
        
        print("\n主要文件检查:")
        for file in main_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"✓ {file} 存在 ({size:,} 字节)")
            else:
                print(f"✗ {file} 不存在")
    
    def check_csv_files(self):
        """检查CSV文件完整性"""
        print("\n" + "="*60)
        print("2. 检查CSV文件完整性")
        print("="*60)
        
        csv_files = []
        total_records = 0
        
        # 检查CSV文件
        for i in range(1, 11):
            filename = f"交通数据_第{i:02d}部分.csv"
            filepath = os.path.join(self.csv_dir, filename)
            
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        csv_reader = csv.reader(f)
                        row_count = sum(1 for row in csv_reader) - 1  # 减去标题行
                    
                    csv_files.append(filename)
                    total_records += row_count
                    print(f"✓ {filename}: {row_count:,} 条记录")
                    
                except Exception as e:
                    print(f"✗ {filename}: 读取失败 - {e}")
            else:
                print(f"✗ {filename}: 文件不存在")
        
        print(f"\nCSV文件汇总:")
        print(f"- 文件数量: {len(csv_files)}/10")
        print(f"- 总记录数: {total_records:,}")
        
        self.check_results['csv_files'] = {
            'count': len(csv_files),
            'total_records': total_records
        }
        
        return total_records
    
    def check_excel_files(self):
        """检查Excel文件完整性"""
        print("\n" + "="*60)
        print("3. 检查Excel文件完整性")
        print("="*60)
        
        excel_files = []
        
        # 检查分割的Excel文件
        for i in range(1, 11):
            filename = f"交通数据_第{i:02d}部分.xlsx"
            filepath = os.path.join(self.excel_dir, filename)
            
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                excel_files.append(filename)
                print(f"✓ {filename}: {size:,} 字节")
            else:
                print(f"✗ {filename}: 文件不存在")
        
        # 检查总体摘要Excel文件
        summary_file = os.path.join(self.excel_dir, '数据分析总体摘要.xlsx')
        if os.path.exists(summary_file):
            size = os.path.getsize(summary_file)
            print(f"✓ 数据分析总体摘要.xlsx: {size:,} 字节")
            excel_files.append('数据分析总体摘要.xlsx')
        else:
            print(f"✗ 数据分析总体摘要.xlsx: 文件不存在")
        
        print(f"\nExcel文件汇总:")
        print(f"- 分割文件: {len([f for f in excel_files if '第' in f])}/10")
        print(f"- 摘要文件: {'1' if '数据分析总体摘要.xlsx' in excel_files else '0'}/1")
        print(f"- 总文件数: {len(excel_files)}/11")
        
        self.check_results['excel_files'] = {
            'count': len(excel_files),
            'has_summary': '数据分析总体摘要.xlsx' in excel_files
        }
    
    def check_analysis_results(self):
        """检查分析结果文件"""
        print("\n" + "="*60)
        print("4. 检查分析结果文件")
        print("="*60)
        
        # 检查JSON统计文件
        json_files = []
        for i in range(1, 11):
            filename = f"统计信息_第{i:02d}部分.json"
            filepath = os.path.join(self.results_dir, filename)
            
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    json_files.append(filename)
                    print(f"✓ {filename}: 有效JSON")
                except Exception as e:
                    print(f"✗ {filename}: JSON格式错误 - {e}")
            else:
                print(f"✗ {filename}: 文件不存在")
        
        # 检查主要分析文件
        main_analysis_files = [
            'overall_summary.json',
            'may_2_period_3_analysis.json',
            '综合分析报告.txt',
            '问题一分析报告.txt',
            '问题二分析报告.txt',
            '问题三分析报告.txt',
            '问题四分析报告.txt'
        ]
        
        print(f"\n主要分析文件:")
        analysis_files_found = 0
        for filename in main_analysis_files:
            filepath = os.path.join(self.results_dir, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"✓ {filename}: {size:,} 字节")
                analysis_files_found += 1
            else:
                print(f"✗ {filename}: 文件不存在")
        
        print(f"\n分析结果汇总:")
        print(f"- JSON统计文件: {len(json_files)}/10")
        print(f"- 主要分析文件: {analysis_files_found}/{len(main_analysis_files)}")
        
        self.check_results['analysis_files'] = {
            'json_count': len(json_files),
            'main_analysis_count': analysis_files_found
        }
    
    def check_visualizations(self):
        """检查可视化文件"""
        print("\n" + "="*60)
        print("5. 检查可视化文件")
        print("="*60)
        
        expected_charts = [
            '问题一_车流量分析.png',
            '问题二_信号灯优化.png',
            '问题三_绕路车分析.png',
            '问题四_管理成效对比.png'
        ]
        
        charts_found = 0
        for chart in expected_charts:
            filepath = os.path.join(self.viz_dir, chart)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"✓ {chart}: {size:,} 字节")
                charts_found += 1
            else:
                print(f"✗ {chart}: 文件不存在")
        
        print(f"\n可视化文件汇总:")
        print(f"- 图表文件: {charts_found}/{len(expected_charts)}")
        
        self.check_results['visualizations'] = {
            'chart_count': charts_found,
            'expected_count': len(expected_charts)
        }
    
    def check_data_consistency(self):
        """检查数据一致性"""
        print("\n" + "="*60)
        print("6. 检查数据一致性")
        print("="*60)
        
        # 检查总体摘要数据
        summary_file = os.path.join(self.results_dir, 'overall_summary.json')
        if os.path.exists(summary_file):
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary = json.load(f)
                
                summary_total = summary.get('数据概况', {}).get('总记录数', 0)
                csv_total = self.check_results.get('csv_files', {}).get('total_records', 0)
                
                print(f"总体摘要记录数: {summary_total:,}")
                print(f"CSV文件记录数: {csv_total:,}")
                
                if summary_total == csv_total:
                    print("✓ 数据记录数一致")
                else:
                    print(f"✗ 数据记录数不一致 (差异: {abs(summary_total - csv_total):,})")
                
                # 检查数据分布
                direction_dist = summary.get('方向分布', {})
                period_dist = summary.get('时段分布', {})
                
                print(f"\n数据分布检查:")
                print(f"- 方向类型: {len(direction_dist)} 种")
                print(f"- 时段类型: {len(period_dist)} 种")
                
                for direction, count in direction_dist.items():
                    print(f"  {direction}: {count:,} 条")
                
            except Exception as e:
                print(f"✗ 读取总体摘要失败: {e}")
        else:
            print("✗ 总体摘要文件不存在")
    
    def check_may_2_analysis(self):
        """检查5月2日第三时段分析"""
        print("\n" + "="*60)
        print("7. 检查5月2日第三时段分析")
        print("="*60)
        
        may_2_file = os.path.join(self.results_dir, 'may_2_period_3_analysis.json')
        if os.path.exists(may_2_file):
            try:
                with open(may_2_file, 'r', encoding='utf-8') as f:
                    may_2_data = json.load(f)
                
                total_flow = may_2_data.get('总车流量', 0)
                unique_vehicles = may_2_data.get('唯一车辆数', 0)
                peak_hour = may_2_data.get('高峰小时', 'N/A')
                peak_flow = may_2_data.get('高峰小时车流量', 0)
                
                print(f"✓ 5月2日第三时段分析:")
                print(f"  - 总车流量: {total_flow} 辆次")
                print(f"  - 唯一车辆: {unique_vehicles} 辆")
                print(f"  - 高峰小时: {peak_hour}时")
                print(f"  - 高峰车流量: {peak_flow} 辆次")
                
                # 检查小时分布
                hour_dist = may_2_data.get('小时分布', {})
                print(f"  - 小时分布数据: {len(hour_dist)} 个小时")
                
                if total_flow > 0 and peak_hour != 'N/A':
                    print("✓ 5月2日第三时段分析数据完整")
                else:
                    print("⚠ 5月2日第三时段分析数据可能不完整")
                
            except Exception as e:
                print(f"✗ 读取5月2日分析失败: {e}")
        else:
            print("✗ 5月2日第三时段分析文件不存在")
    
    def generate_integrity_report(self):
        """生成完整性检查报告"""
        print("\n" + "="*60)
        print("8. 生成完整性检查报告")
        print("="*60)
        
        report = []
        report.append("数据完整性检查报告")
        report.append("="*40)
        report.append("")
        
        # CSV文件检查结果
        csv_results = self.check_results.get('csv_files', {})
        report.append(f"CSV文件: {csv_results.get('count', 0)}/10 个")
        report.append(f"总记录数: {csv_results.get('total_records', 0):,} 条")
        report.append("")
        
        # Excel文件检查结果
        excel_results = self.check_results.get('excel_files', {})
        report.append(f"Excel文件: {excel_results.get('count', 0)}/11 个")
        report.append(f"包含摘要文件: {'是' if excel_results.get('has_summary', False) else '否'}")
        report.append("")
        
        # 分析文件检查结果
        analysis_results = self.check_results.get('analysis_files', {})
        report.append(f"JSON统计文件: {analysis_results.get('json_count', 0)}/10 个")
        report.append(f"主要分析文件: {analysis_results.get('main_analysis_count', 0)}/7 个")
        report.append("")
        
        # 可视化文件检查结果
        viz_results = self.check_results.get('visualizations', {})
        report.append(f"可视化图表: {viz_results.get('chart_count', 0)}/{viz_results.get('expected_count', 4)} 个")
        report.append("")
        
        # 总体评估
        total_files_expected = 10 + 11 + 10 + 7 + 4  # CSV + Excel + JSON + 分析 + 图表
        total_files_found = (csv_results.get('count', 0) + 
                           excel_results.get('count', 0) + 
                           analysis_results.get('json_count', 0) + 
                           analysis_results.get('main_analysis_count', 0) + 
                           viz_results.get('chart_count', 0))
        
        completion_rate = (total_files_found / total_files_expected * 100) if total_files_expected > 0 else 0
        
        report.append(f"总体完成度: {completion_rate:.1f}% ({total_files_found}/{total_files_expected})")
        
        if completion_rate >= 95:
            report.append("评估结果: ✓ 优秀 - 数据完整性良好")
        elif completion_rate >= 85:
            report.append("评估结果: ✓ 良好 - 数据基本完整")
        elif completion_rate >= 70:
            report.append("评估结果: ⚠ 一般 - 部分数据缺失")
        else:
            report.append("评估结果: ✗ 较差 - 数据严重缺失")
        
        # 保存报告
        report_content = '\n'.join(report)
        with open(os.path.join(self.results_dir, '数据完整性检查报告.txt'), 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(report_content)
        print(f"\n✓ 完整性检查报告已保存")
        
        return completion_rate

def main():
    """主函数"""
    print("2024年数模大赛E题 - 数据完整性检查")
    print("="*60)
    
    try:
        checker = DataIntegrityChecker()
        
        # 执行各项检查
        checker.check_directory_structure()
        checker.check_csv_files()
        checker.check_excel_files()
        checker.check_analysis_results()
        checker.check_visualizations()
        checker.check_data_consistency()
        checker.check_may_2_analysis()
        
        # 生成最终报告
        completion_rate = checker.generate_integrity_report()
        
        print("\n" + "="*60)
        print("数据完整性检查完成！")
        print("="*60)
        print(f"总体完成度: {completion_rate:.1f}%")
        
        if completion_rate >= 95:
            print("🎉 数据完整性优秀！所有文件基本完整。")
        elif completion_rate >= 85:
            print("✅ 数据完整性良好！大部分文件完整。")
        else:
            print("⚠️ 数据完整性需要改进，请检查缺失的文件。")
        
    except Exception as e:
        print(f"检查过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
