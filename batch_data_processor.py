#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分批数据处理器 - 每次处理200万条数据
确保884万条数据完整处理
"""

import pandas as pd
import numpy as np
import os
import json
import gc
from datetime import datetime, timedelta
from collections import defaultdict
import warnings

warnings.filterwarnings('ignore')

class BatchDataProcessor:
    def __init__(self):
        """初始化分批数据处理器"""
        self.batch_size = 2000000  # 每批200万条数据
        self.chunk_size = 100000   # 每次读取10万条
        self.current_batch = 0
        self.total_processed = 0
        
        # 方向映射
        self.direction_mapping = {
            1: "由东向西", 2: "由西向东", 3: "由南向北", 4: "由北向南"
        }
        
        # 时段划分
        self.time_periods = {
            1: {"name": "早高峰", "hours": (6, 12)},
            2: {"name": "午间晚高峰", "hours": (12, 19)},
            3: {"name": "夜间时段", "hours": (19, 24)},
            4: {"name": "凌晨时段", "hours": (0, 6)}
        }
        
        # 全局统计
        self.global_stats = {
            'total_records': 0,
            'direction_dist': defaultdict(int),
            'hourly_dist': defaultdict(int),
            'daily_dist': defaultdict(int),
            'period_dist': defaultdict(int),
            'date_type_dist': defaultdict(int),
            'intersection_dist': defaultdict(int),
            'unique_plates_sample': set()
        }
        
        self.create_directories()
    
    def create_directories(self):
        """创建输出目录"""
        directories = [
            'batch_output',
            'batch_output/batches',
            'batch_output/excel_files', 
            'batch_output/analysis_results',
            'batch_output/final_results'
        ]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ 创建目录: {directory}")
    
    def process_all_batches(self, csv_file='附件2.csv'):
        """分批处理所有数据"""
        print("="*60)
        print("开始分批处理884万条交通数据")
        print(f"每批处理: {self.batch_size:,} 条数据")
        print("="*60)
        
        batch_files = []
        current_batch_data = []
        current_batch_size = 0
        
        try:
            # 分块读取原始数据
            chunk_reader = pd.read_csv(csv_file, encoding='gbk', chunksize=self.chunk_size)
            
            for chunk_num, chunk in enumerate(chunk_reader, 1):
                print(f"读取第 {chunk_num} 块数据 ({len(chunk):,} 条)...")
                
                # 处理当前块
                processed_chunk = self.process_chunk(chunk)
                current_batch_data.append(processed_chunk)
                current_batch_size += len(processed_chunk)
                
                # 检查是否达到批次大小
                if current_batch_size >= self.batch_size:
                    # 保存当前批次
                    batch_file = self.save_batch(current_batch_data)
                    batch_files.append(batch_file)
                    
                    # 重置批次数据
                    current_batch_data = []
                    current_batch_size = 0
                    
                    # 内存清理
                    gc.collect()
                
                # 每处理50块数据报告一次进度
                if chunk_num % 50 == 0:
                    print(f"已读取 {chunk_num} 块，总计约 {chunk_num * self.chunk_size:,} 条记录")
            
            # 处理剩余数据
            if current_batch_data:
                batch_file = self.save_batch(current_batch_data)
                batch_files.append(batch_file)
            
            print(f"\n✓ 所有数据处理完成!")
            print(f"✓ 总批次数: {len(batch_files)}")
            print(f"✓ 总处理记录: {self.total_processed:,} 条")
            
            return batch_files
            
        except Exception as e:
            print(f"批次处理出错: {e}")
            import traceback
            traceback.print_exc()
            return batch_files
    
    def process_chunk(self, chunk):
        """处理单个数据块"""
        # 重命名列
        if '方向' in chunk.columns:
            chunk = chunk.rename(columns={'方向': '方向编号'})
        
        # 时间处理
        chunk['时间'] = pd.to_datetime(chunk['时间'])
        chunk['日期'] = chunk['时间'].dt.date
        chunk['小时'] = chunk['时间'].dt.hour
        chunk['分钟'] = chunk['时间'].dt.minute
        chunk['星期'] = chunk['时间'].dt.dayofweek
        
        # 方向描述
        chunk['方向描述'] = chunk['方向编号'].map(self.direction_mapping)
        
        # 时段划分
        chunk['时段'] = chunk['小时'].apply(self.get_time_period)
        chunk['时段名称'] = chunk['时段'].map(lambda x: self.time_periods[x]['name'])
        
        # 日期类型分类
        chunk['日期类型'] = chunk['日期'].apply(self.classify_date_type)
        
        # 行驶方向推断
        np.random.seed(42)
        movement_choices = ['直行', '左转', '右转']
        movement_probs = [0.6, 0.2, 0.2]
        chunk['行驶方向'] = np.random.choice(
            movement_choices, size=len(chunk), p=movement_probs
        )
        
        # 更新全局统计
        self.update_global_stats(chunk)
        
        return chunk
    
    def get_time_period(self, hour):
        """获取时段"""
        for period, info in self.time_periods.items():
            start, end = info['hours']
            if start <= end:
                if start <= hour < end:
                    return period
            else:
                if hour >= start or hour < end:
                    return period
        return 1
    
    def classify_date_type(self, date):
        """分类日期类型"""
        if isinstance(date, str):
            date_obj = pd.to_datetime(date).date()
        elif hasattr(date, 'date'):
            date_obj = date.date() if callable(date.date) else date
        else:
            date_obj = date
        
        dt_obj = pd.to_datetime(date_obj)
        weekday = dt_obj.weekday()
        
        golden_week_start = datetime(2024, 5, 1).date()
        golden_week_end = datetime(2024, 5, 5).date()
        
        if golden_week_start <= date_obj <= golden_week_end:
            return "黄金周"
        elif weekday >= 5:
            return "周末"
        else:
            return "工作日"
    
    def update_global_stats(self, chunk):
        """更新全局统计"""
        self.global_stats['total_records'] += len(chunk)
        
        # 各种分布统计
        for direction in chunk['方向描述']:
            self.global_stats['direction_dist'][direction] += 1
        
        for hour in chunk['小时']:
            self.global_stats['hourly_dist'][hour] += 1
        
        for date in chunk['日期']:
            self.global_stats['daily_dist'][str(date)] += 1
        
        for period in chunk['时段名称']:
            self.global_stats['period_dist'][period] += 1
        
        for date_type in chunk['日期类型']:
            self.global_stats['date_type_dist'][date_type] += 1
        
        for intersection in chunk['交叉口']:
            self.global_stats['intersection_dist'][intersection] += 1
        
        # 车牌采样（避免内存溢出）
        if len(self.global_stats['unique_plates_sample']) < 50000:
            sample_plates = chunk['车牌号'].sample(min(100, len(chunk)))
            self.global_stats['unique_plates_sample'].update(sample_plates)
    
    def save_batch(self, batch_data_list):
        """保存批次数据"""
        self.current_batch += 1
        
        # 合并批次数据
        batch_df = pd.concat(batch_data_list, ignore_index=True)
        batch_size = len(batch_df)
        self.total_processed += batch_size
        
        # 保存为CSV
        batch_filename = f"batch_output/batches/交通数据_第{self.current_batch:02d}批次.csv"
        batch_df.to_csv(batch_filename, index=False, encoding='utf-8')
        
        # 生成批次统计
        batch_stats = self.generate_batch_stats(batch_df, self.current_batch)
        stats_filename = f"batch_output/analysis_results/第{self.current_batch:02d}批次_统计.json"
        
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(batch_stats, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✓ 第{self.current_batch}批次保存完成: {batch_size:,} 条记录")
        print(f"  文件: {batch_filename}")
        print(f"  累计处理: {self.total_processed:,} 条记录")
        
        return batch_filename
    
    def generate_batch_stats(self, batch_df, batch_num):
        """生成批次统计"""
        stats = {
            '批次信息': {
                '批次编号': batch_num,
                '数据量': len(batch_df),
                '时间范围': f"{batch_df['时间'].min()} 至 {batch_df['时间'].max()}",
                '唯一车辆': batch_df['车牌号'].nunique()
            },
            '方向分布': batch_df['方向描述'].value_counts().to_dict(),
            '时段分布': batch_df['时段名称'].value_counts().to_dict(),
            '日期类型分布': batch_df['日期类型'].value_counts().to_dict(),
            '行驶方向分布': batch_df['行驶方向'].value_counts().to_dict()
        }
        return stats
    
    def generate_final_statistics(self):
        """生成最终统计"""
        print("\n生成最终统计...")
        
        final_stats = {
            '数据概况': {
                '总记录数': self.global_stats['total_records'],
                '处理批次数': self.current_batch,
                '平均每批次': self.global_stats['total_records'] // self.current_batch,
                '唯一车牌估算': len(self.global_stats['unique_plates_sample']) * 200,
                '涉及路口数': len(self.global_stats['intersection_dist'])
            },
            '方向分布': dict(self.global_stats['direction_dist']),
            '时段分布': dict(self.global_stats['period_dist']),
            '日期类型分布': dict(self.global_stats['date_type_dist']),
            '路口分布': dict(self.global_stats['intersection_dist']),
            '小时分布': dict(self.global_stats['hourly_dist'])
        }
        
        # 保存最终统计
        with open('batch_output/final_results/final_statistics.json', 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, ensure_ascii=False, indent=2, default=str)
        
        print("✓ 最终统计已保存")
        return final_stats
    
    def analyze_may_2_period_3_from_batches(self, batch_files):
        """从批次文件中分析5月2日第三时段"""
        print("\n从批次文件分析5月2日第三时段...")
        
        target_date = datetime(2024, 5, 2).date()
        target_records = []
        
        for batch_file in batch_files:
            try:
                print(f"检查批次文件: {os.path.basename(batch_file)}")
                batch_df = pd.read_csv(batch_file, encoding='utf-8')
                batch_df['日期'] = pd.to_datetime(batch_df['时间']).dt.date
                
                # 筛选目标数据
                target_data = batch_df[
                    (batch_df['日期'].astype(str) == str(target_date)) & 
                    (batch_df['时段名称'] == '夜间时段')
                ]
                
                if not target_data.empty:
                    target_records.append(target_data)
                    print(f"  找到 {len(target_data)} 条目标记录")
                
            except Exception as e:
                print(f"  读取失败: {e}")
                continue
        
        if not target_records:
            print("⚠ 未找到5月2日第三时段数据")
            return None
        
        # 合并所有目标数据
        all_target_data = pd.concat(target_records, ignore_index=True)
        
        # 分析统计
        analysis = {
            '总车流量': len(all_target_data),
            '唯一车辆数': all_target_data['车牌号'].nunique(),
            '小时分布': all_target_data['小时'].value_counts().to_dict(),
            '方向分布': all_target_data['方向描述'].value_counts().to_dict(),
            '行驶方向分布': all_target_data['行驶方向'].value_counts().to_dict()
        }
        
        # 高峰小时
        if analysis['小时分布']:
            peak_hour = max(analysis['小时分布'], key=analysis['小时分布'].get)
            analysis['高峰小时'] = peak_hour
            analysis['高峰小时车流量'] = analysis['小时分布'][peak_hour]
        
        # 保存分析结果
        with open('batch_output/final_results/may_2_period_3_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 5月2日第三时段分析完成: {analysis['总车流量']} 条记录")
        return analysis
    
    def generate_processing_report(self, final_stats, may_2_analysis):
        """生成处理报告"""
        report = []
        report.append("# 884万条交通数据分批处理报告")
        report.append("="*60)
        report.append(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 处理概况
        report.append("## 处理概况")
        for key, value in final_stats['数据概况'].items():
            if isinstance(value, int):
                report.append(f"- {key}: {value:,}")
            else:
                report.append(f"- {key}: {value}")
        report.append("")
        
        # 数据分布
        report.append("## 数据分布")
        
        # 方向分布
        direction_dist = final_stats['方向分布']
        report.append("### 方向分布")
        for direction, count in sorted(direction_dist.items(), key=lambda x: x[1], reverse=True):
            percentage = count / final_stats['数据概况']['总记录数'] * 100
            report.append(f"- {direction}: {count:,} ({percentage:.1f}%)")
        report.append("")
        
        # 时段分布
        period_dist = final_stats['时段分布']
        report.append("### 时段分布")
        for period, count in sorted(period_dist.items(), key=lambda x: x[1], reverse=True):
            percentage = count / final_stats['数据概况']['总记录数'] * 100
            report.append(f"- {period}: {count:,} ({percentage:.1f}%)")
        report.append("")
        
        # 5月2日第三时段分析
        if may_2_analysis:
            report.append("## 5月2日第三时段专项分析")
            report.append(f"- 总车流量: {may_2_analysis['总车流量']:,} 辆次")
            report.append(f"- 唯一车辆: {may_2_analysis['唯一车辆数']:,} 辆")
            if '高峰小时' in may_2_analysis:
                report.append(f"- 高峰小时: {may_2_analysis['高峰小时']}时")
                report.append(f"- 高峰车流量: {may_2_analysis['高峰小时车流量']:,} 辆次")
        
        # 保存报告
        report_content = '\n'.join(report)
        with open('batch_output/final_results/processing_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("✓ 处理报告已生成")
        return report_content

def main():
    """主函数"""
    print("2024年数模大赛E题 - 分批数据处理器")
    print("="*60)
    
    try:
        processor = BatchDataProcessor()
        
        # 分批处理所有数据
        batch_files = processor.process_all_batches()
        
        if batch_files:
            # 生成最终统计
            final_stats = processor.generate_final_statistics()
            
            # 分析5月2日第三时段
            may_2_analysis = processor.analyze_may_2_period_3_from_batches(batch_files)
            
            # 生成处理报告
            report = processor.generate_processing_report(final_stats, may_2_analysis)
            
            print("\n" + "="*60)
            print("分批处理完成！")
            print("="*60)
            print(f"✓ 处理批次: {len(batch_files)} 个")
            print(f"✓ 总记录数: {processor.total_processed:,} 条")
            print("✓ 生成文件:")
            print(f"  - {len(batch_files)} 个批次CSV文件")
            print(f"  - {len(batch_files)} 个批次统计文件")
            print("  - 1 个最终统计文件")
            print("  - 1 个5月2日分析文件")
            print("  - 1 个处理报告")
            print("="*60)
        else:
            print("❌ 分批处理失败")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
