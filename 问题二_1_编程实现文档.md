# 问题二：信号灯优化模型 - 编程实现文档

## 📋 **编程任务概述**

### 核心任务
- 基于5月2日第三时段数据建立信号灯优化模型
- 实现四相位信号灯控制算法
- 计算最优绿灯时间分配方案
- 评估信号灯控制效果和服务水平

### 技术要求
- 实现Webster延误模型
- 开发多种优化算法（遗传算法、梯度优化等）
- 建立相位冲突检测机制
- 生成信号灯配时方案和性能评估报告

## 💻 **核心算法实现**

### 1. 信号灯相位设计算法

```python
class SignalPhaseDesigner:
    def __init__(self):
        """初始化信号相位设计器"""
        self.phases = {
            'Phase_1': {
                'name': '东西直行右转',
                'directions': [1, 3],  # 由东向西, 由南向北
                'movements': ['直行', '右转'],
                'conflict_phases': ['Phase_2', 'Phase_4']
            },
            'Phase_2': {
                'name': '东西左转',
                'directions': [1, 3],  # 由东向西, 由南向北
                'movements': ['左转'],
                'conflict_phases': ['Phase_1', 'Phase_3', 'Phase_4']
            },
            'Phase_3': {
                'name': '南北直行右转',
                'directions': [2, 4],  # 由西向东, 由北向南
                'movements': ['直行', '右转'],
                'conflict_phases': ['Phase_2', 'Phase_4']
            },
            'Phase_4': {
                'name': '南北左转',
                'directions': [2, 4],  # 由西向东, 由北向南
                'movements': ['左转'],
                'conflict_phases': ['Phase_1', 'Phase_2', 'Phase_3']
            }
        }
        
        # 相位冲突矩阵
        self.conflict_matrix = self.build_conflict_matrix()
    
    def build_conflict_matrix(self):
        """构建相位冲突矩阵"""
        phases = list(self.phases.keys())
        n = len(phases)
        matrix = [[0 for _ in range(n)] for _ in range(n)]
        
        for i, phase_i in enumerate(phases):
            for j, phase_j in enumerate(phases):
                if phase_j in self.phases[phase_i]['conflict_phases']:
                    matrix[i][j] = 1
        
        return matrix
    
    def analyze_phase_demands(self, traffic_data):
        """分析各相位的交通需求"""
        phase_demands = {}
        
        for phase_name, phase_info in self.phases.items():
            demand = 0
            
            # 计算该相位的总需求
            for direction in phase_info['directions']:
                for movement in phase_info['movements']:
                    # 筛选符合条件的交通流
                    filtered_data = traffic_data[
                        (traffic_data['方向编号'] == direction) & 
                        (traffic_data['行驶方向'] == movement)
                    ]
                    demand += len(filtered_data)
            
            phase_demands[phase_name] = demand
        
        return phase_demands
    
    def validate_phase_compatibility(self, active_phases):
        """验证相位兼容性"""
        phases = list(self.phases.keys())
        
        for i, phase_i in enumerate(active_phases):
            for j, phase_j in enumerate(active_phases):
                if i != j:
                    idx_i = phases.index(phase_i)
                    idx_j = phases.index(phase_j)
                    
                    if self.conflict_matrix[idx_i][idx_j] == 1:
                        return False, f"相位冲突: {phase_i} 与 {phase_j}"
        
        return True, "相位兼容"
```

### 2. Webster延误模型实现

```python
class WebsterDelayModel:
    def __init__(self):
        """初始化Webster延误模型"""
        self.saturation_flow_rate = 1800  # 饱和流率 (辆/小时/车道)
        self.default_cycle_time = 120     # 默认周期时长 (秒)
        self.yellow_time = 3              # 黄灯时间 (秒)
        self.all_red_time = 2             # 全红时间 (秒)
    
    def calculate_delay(self, demand, green_time, cycle_time=None):
        """计算Webster延误时间"""
        if cycle_time is None:
            cycle_time = self.default_cycle_time
        
        # 计算基本参数
        green_ratio = green_time / cycle_time  # 绿信比
        capacity = green_ratio * self.saturation_flow_rate  # 通行能力
        
        if capacity <= 0:
            return float('inf')
        
        saturation_degree = demand / capacity  # 饱和度
        
        if saturation_degree >= 1.0:
            # 过饱和情况
            return 300  # 返回一个很大的延误值
        
        # Webster延误公式
        # 第一项：均匀延误
        uniform_delay = (cycle_time * (1 - green_ratio)**2) / (2 * (1 - saturation_degree))
        
        # 第二项：随机延误
        random_delay = (saturation_degree**2) / (2 * demand * (1 - saturation_degree)) if demand > 0 else 0
        
        total_delay = uniform_delay + random_delay
        
        return max(0, total_delay)
    
    def calculate_total_delay(self, phase_demands, green_times, cycle_time=None):
        """计算总延误时间"""
        total_delay = 0
        total_vehicles = sum(phase_demands.values())
        
        if total_vehicles == 0:
            return 0
        
        for phase, demand in phase_demands.items():
            if phase in green_times and demand > 0:
                green_time = green_times[phase]
                delay = self.calculate_delay(demand, green_time, cycle_time)
                total_delay += delay * demand
        
        return total_delay / total_vehicles
    
    def calculate_capacity(self, green_time, cycle_time=None):
        """计算通行能力"""
        if cycle_time is None:
            cycle_time = self.default_cycle_time
        
        green_ratio = green_time / cycle_time
        return green_ratio * self.saturation_flow_rate
    
    def calculate_service_level(self, average_delay):
        """计算服务水平"""
        if average_delay < 10:
            return 'A', '优秀'
        elif average_delay < 20:
            return 'B', '良好'
        elif average_delay < 35:
            return 'C', '一般'
        elif average_delay < 55:
            return 'D', '较差'
        elif average_delay < 80:
            return 'E', '差'
        else:
            return 'F', '很差'
```

### 3. 信号灯优化算法

```python
class SignalOptimizer:
    def __init__(self, cycle_time=120):
        """初始化信号灯优化器"""
        self.cycle_time = cycle_time
        self.min_green_time = 15
        self.max_green_time = 60
        self.yellow_time = 3
        self.all_red_time = 2
        self.delay_model = WebsterDelayModel()
    
    def optimize_by_proportion(self, phase_demands):
        """比例分配法优化"""
        total_demand = sum(phase_demands.values())
        if total_demand == 0:
            return {phase: 25 for phase in phase_demands.keys()}
        
        # 计算可用绿灯时间
        available_green = self.cycle_time - len(phase_demands) * (self.yellow_time + self.all_red_time)
        
        # 按需求比例分配
        green_times = {}
        for phase, demand in phase_demands.items():
            proportion = demand / total_demand
            green_time = max(self.min_green_time, proportion * available_green)
            green_times[phase] = min(self.max_green_time, green_time)
        
        # 调整总时间
        total_green = sum(green_times.values())
        if total_green != available_green:
            adjustment_factor = available_green / total_green
            for phase in green_times:
                green_times[phase] *= adjustment_factor
                green_times[phase] = max(self.min_green_time, 
                                       min(self.max_green_time, green_times[phase]))
        
        return green_times
    
    def optimize_by_gradient(self, phase_demands, max_iterations=100):
        """梯度优化法"""
        from scipy.optimize import minimize
        
        def objective_function(green_times_array):
            """目标函数：最小化总延误"""
            green_times = dict(zip(phase_demands.keys(), green_times_array))
            return self.delay_model.calculate_total_delay(phase_demands, green_times, self.cycle_time)
        
        def constraint_total_time(green_times_array):
            """约束：总绿灯时间"""
            available_green = self.cycle_time - len(phase_demands) * (self.yellow_time + self.all_red_time)
            return available_green - sum(green_times_array)
        
        # 初始猜测
        initial_guess = list(self.optimize_by_proportion(phase_demands).values())
        
        # 边界约束
        bounds = [(self.min_green_time, self.max_green_time) for _ in phase_demands]
        
        # 等式约束
        constraints = {'type': 'eq', 'fun': constraint_total_time}
        
        # 优化求解
        result = minimize(
            objective_function,
            initial_guess,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': max_iterations}
        )
        
        if result.success:
            optimal_times = result.x
        else:
            # 如果优化失败，使用比例分配
            optimal_times = list(self.optimize_by_proportion(phase_demands).values())
        
        return dict(zip(phase_demands.keys(), optimal_times))
    
    def optimize_by_genetic_algorithm(self, phase_demands, population_size=50, generations=100):
        """遗传算法优化"""
        import random
        import numpy as np
        
        def create_individual():
            """创建个体（配时方案）"""
            available_green = self.cycle_time - len(phase_demands) * (self.yellow_time + self.all_red_time)
            
            # 随机生成绿灯时间
            individual = []
            remaining_time = available_green
            
            for i, phase in enumerate(phase_demands.keys()):
                if i == len(phase_demands) - 1:
                    # 最后一个相位分配剩余时间
                    green_time = max(self.min_green_time, remaining_time)
                else:
                    # 随机分配
                    max_time = min(self.max_green_time, remaining_time - (len(phase_demands) - i - 1) * self.min_green_time)
                    green_time = random.uniform(self.min_green_time, max_time)
                    remaining_time -= green_time
                
                individual.append(green_time)
            
            return individual
        
        def fitness(individual):
            """适应度函数"""
            green_times = dict(zip(phase_demands.keys(), individual))
            delay = self.delay_model.calculate_total_delay(phase_demands, green_times, self.cycle_time)
            return 1 / (1 + delay)  # 延误越小，适应度越高
        
        def crossover(parent1, parent2):
            """交叉操作"""
            point = random.randint(1, len(parent1) - 1)
            child1 = parent1[:point] + parent2[point:]
            child2 = parent2[:point] + parent1[point:]
            return child1, child2
        
        def mutate(individual, mutation_rate=0.1):
            """变异操作"""
            for i in range(len(individual)):
                if random.random() < mutation_rate:
                    # 小幅调整
                    adjustment = random.uniform(-5, 5)
                    individual[i] = max(self.min_green_time, 
                                      min(self.max_green_time, individual[i] + adjustment))
            
            # 确保总时间约束
            available_green = self.cycle_time - len(phase_demands) * (self.yellow_time + self.all_red_time)
            total_green = sum(individual)
            if total_green != available_green:
                factor = available_green / total_green
                for i in range(len(individual)):
                    individual[i] *= factor
                    individual[i] = max(self.min_green_time, 
                                      min(self.max_green_time, individual[i]))
            
            return individual
        
        # 初始化种群
        population = [create_individual() for _ in range(population_size)]
        
        # 进化过程
        for generation in range(generations):
            # 计算适应度
            fitness_scores = [fitness(ind) for ind in population]
            
            # 选择（锦标赛选择）
            new_population = []
            for _ in range(population_size):
                tournament_size = 3
                tournament_indices = random.sample(range(population_size), tournament_size)
                winner_index = max(tournament_indices, key=lambda i: fitness_scores[i])
                new_population.append(population[winner_index].copy())
            
            # 交叉
            for i in range(0, population_size - 1, 2):
                if random.random() < 0.8:  # 交叉概率
                    new_population[i], new_population[i + 1] = crossover(
                        new_population[i], new_population[i + 1]
                    )
            
            # 变异
            for i in range(population_size):
                new_population[i] = mutate(new_population[i])
            
            population = new_population
        
        # 返回最优解
        best_individual = max(population, key=fitness)
        return dict(zip(phase_demands.keys(), best_individual))
```

### 4. 性能评估算法

```python
class SignalPerformanceEvaluator:
    def __init__(self):
        """初始化性能评估器"""
        self.delay_model = WebsterDelayModel()
    
    def evaluate_signal_performance(self, phase_demands, green_times, cycle_time=120):
        """评估信号灯性能"""
        performance_metrics = {}
        
        # 基本指标
        total_demand = sum(phase_demands.values())
        total_green = sum(green_times.values())
        
        performance_metrics['total_demand'] = total_demand
        performance_metrics['total_green_time'] = total_green
        performance_metrics['cycle_time'] = cycle_time
        
        # 延误指标
        avg_delay = self.delay_model.calculate_total_delay(phase_demands, green_times, cycle_time)
        performance_metrics['average_delay'] = avg_delay
        
        # 服务水平
        service_level, service_desc = self.delay_model.calculate_service_level(avg_delay)
        performance_metrics['service_level'] = service_level
        performance_metrics['service_description'] = service_desc
        
        # 各相位指标
        phase_metrics = {}
        for phase, demand in phase_demands.items():
            if phase in green_times:
                green_time = green_times[phase]
                
                # 通行能力
                capacity = self.delay_model.calculate_capacity(green_time, cycle_time)
                
                # 饱和度
                saturation = demand / capacity if capacity > 0 else float('inf')
                
                # 利用率
                utilization = green_time / total_green if total_green > 0 else 0
                
                # 相位延误
                phase_delay = self.delay_model.calculate_delay(demand, green_time, cycle_time)
                
                phase_metrics[phase] = {
                    'demand': demand,
                    'green_time': green_time,
                    'capacity': capacity,
                    'saturation': saturation,
                    'utilization': utilization,
                    'delay': phase_delay
                }
        
        performance_metrics['phase_metrics'] = phase_metrics
        
        # 效率指标
        performance_metrics['efficiency'] = self.calculate_efficiency(phase_demands, green_times)
        
        return performance_metrics
    
    def calculate_efficiency(self, phase_demands, green_times):
        """计算效率指标"""
        total_demand = sum(phase_demands.values())
        total_capacity = 0
        
        for phase, demand in phase_demands.items():
            if phase in green_times:
                green_time = green_times[phase]
                capacity = self.delay_model.calculate_capacity(green_time)
                total_capacity += capacity
        
        efficiency = total_demand / total_capacity if total_capacity > 0 else 0
        return min(1.0, efficiency)
    
    def compare_scenarios(self, phase_demands, scenarios):
        """比较不同配时方案"""
        comparison_results = {}
        
        for scenario_name, green_times in scenarios.items():
            performance = self.evaluate_signal_performance(phase_demands, green_times)
            comparison_results[scenario_name] = performance
        
        # 排序
        sorted_scenarios = sorted(
            comparison_results.items(),
            key=lambda x: x[1]['average_delay']
        )
        
        return comparison_results, sorted_scenarios
```

### 5. 可视化与报告生成

```python
import matplotlib.pyplot as plt
import numpy as np

class SignalVisualization:
    def __init__(self):
        """初始化可视化器"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def plot_optimization_results(self, phase_demands, optimal_timing, performance_metrics):
        """绘制优化结果图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 相位需求与配时对比
        self.plot_demand_vs_timing(phase_demands, optimal_timing, axes[0, 0])
        
        # 2. 相位利用率饼图
        self.plot_utilization_pie(performance_metrics, axes[0, 1])
        
        # 3. 延误分析
        self.plot_delay_analysis(performance_metrics, axes[1, 0])
        
        # 4. 性能雷达图
        self.plot_performance_radar(performance_metrics, axes[1, 1])
        
        plt.tight_layout()
        plt.savefig('问题二_信号灯优化.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_demand_vs_timing(self, phase_demands, optimal_timing, ax):
        """绘制需求与配时对比图"""
        phases = list(phase_demands.keys())
        demands = list(phase_demands.values())
        timings = [optimal_timing.get(phase, 0) for phase in phases]
        
        x = np.arange(len(phases))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, demands, width, label='交通需求', alpha=0.8)
        bars2 = ax.bar(x + width/2, [t*10 for t in timings], width, label='绿灯时间×10', alpha=0.8)
        
        ax.set_xlabel('信号相位')
        ax.set_ylabel('数量')
        ax.set_title('相位需求与配时对比')
        ax.set_xticks(x)
        ax.set_xticklabels([p.replace('Phase_', 'P') for p in phases])
        ax.legend()
        
        # 添加数值标签
        for bar, value in zip(bars1, demands):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                   f'{value}', ha='center', va='bottom')
        
        for bar, value in zip(bars2, timings):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                   f'{value:.1f}s', ha='center', va='bottom')
    
    def plot_utilization_pie(self, performance_metrics, ax):
        """绘制利用率饼图"""
        phase_metrics = performance_metrics.get('phase_metrics', {})
        
        phases = []
        utilizations = []
        
        for phase, metrics in phase_metrics.items():
            phases.append(phase.replace('Phase_', 'P'))
            utilizations.append(metrics.get('utilization', 0))
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        wedges, texts, autotexts = ax.pie(utilizations, labels=phases, autopct='%1.1f%%',
                                         colors=colors, startangle=90)
        ax.set_title('相位时间利用率分布')
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    def plot_delay_analysis(self, performance_metrics, ax):
        """绘制延误分析图"""
        phase_metrics = performance_metrics.get('phase_metrics', {})
        
        phases = []
        delays = []
        
        for phase, metrics in phase_metrics.items():
            phases.append(phase.replace('Phase_', 'P'))
            delays.append(metrics.get('delay', 0))
        
        bars = ax.bar(phases, delays, color='orange', alpha=0.7)
        ax.set_xlabel('信号相位')
        ax.set_ylabel('延误时间(秒)')
        ax.set_title('各相位延误分析')
        
        # 添加平均延误线
        avg_delay = performance_metrics.get('average_delay', 0)
        ax.axhline(y=avg_delay, color='red', linestyle='--', 
                  label=f'平均延误: {avg_delay:.1f}s')
        ax.legend()
        
        # 添加数值标签
        for bar, delay in zip(bars, delays):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{delay:.1f}s', ha='center', va='bottom')
    
    def plot_performance_radar(self, performance_metrics, ax):
        """绘制性能雷达图"""
        # 性能指标
        categories = ['效率', '服务水平', '利用率', '均衡性']
        
        # 计算各项指标得分（0-100）
        efficiency = performance_metrics.get('efficiency', 0) * 100
        
        # 服务水平转换为得分
        service_level = performance_metrics.get('service_level', 'F')
        service_scores = {'A': 100, 'B': 85, 'C': 70, 'D': 55, 'E': 40, 'F': 25}
        service_score = service_scores.get(service_level, 25)
        
        # 平均利用率
        phase_metrics = performance_metrics.get('phase_metrics', {})
        utilizations = [m.get('utilization', 0) for m in phase_metrics.values()]
        avg_utilization = np.mean(utilizations) * 100 if utilizations else 0
        
        # 均衡性（利用率标准差的倒数）
        balance_score = max(0, 100 - np.std(utilizations) * 100) if utilizations else 0
        
        values = [efficiency, service_score, avg_utilization, balance_score]
        
        # 雷达图
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=2, label='当前方案')
        ax.fill(angles, values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 100)
        ax.set_title('信号灯性能雷达图')
        ax.grid(True)
```

### 6. 主程序集成

```python
def main():
    """问题二主程序"""
    print("问题二：信号灯优化模型")
    print("="*50)
    
    try:
        # 1. 初始化组件
        phase_designer = SignalPhaseDesigner()
        optimizer = SignalOptimizer()
        evaluator = SignalPerformanceEvaluator()
        visualizer = SignalVisualization()
        
        # 2. 加载5月2日第三时段数据
        print("加载5月2日第三时段数据...")
        traffic_data = load_may_2_period_3_data()
        
        # 3. 分析相位需求
        print("分析相位需求...")
        phase_demands = phase_designer.analyze_phase_demands(traffic_data)
        
        # 4. 多种方法优化
        print("执行信号灯优化...")
        
        # 比例分配法
        timing_proportion = optimizer.optimize_by_proportion(phase_demands)
        
        # 梯度优化法
        timing_gradient = optimizer.optimize_by_gradient(phase_demands)
        
        # 遗传算法
        timing_genetic = optimizer.optimize_by_genetic_algorithm(phase_demands)
        
        # 5. 性能评估
        print("评估优化效果...")
        scenarios = {
            '比例分配': timing_proportion,
            '梯度优化': timing_gradient,
            '遗传算法': timing_genetic
        }
        
        comparison_results, sorted_scenarios = evaluator.compare_scenarios(
            phase_demands, scenarios
        )
        
        # 6. 选择最优方案
        best_scenario_name, best_performance = sorted_scenarios[0]
        best_timing = scenarios[best_scenario_name]
        
        # 7. 生成可视化
        print("生成可视化图表...")
        visualizer.plot_optimization_results(
            phase_demands, best_timing, best_performance
        )
        
        # 8. 生成报告
        print("生成优化报告...")
        generate_optimization_report(
            phase_demands, best_timing, best_performance, comparison_results
        )
        
        print("✓ 问题二分析完成！")
        return best_timing, best_performance
        
    except Exception as e:
        print(f"优化过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    main()
```

## 📊 **性能优化与扩展**

### 内存优化策略
- 使用生成器处理大数据集
- 及时释放不需要的变量
- 采用稀疏矩阵存储冲突关系

### 算法优化策略
- 并行计算多个优化方案
- 缓存重复计算结果
- 使用向量化操作提高效率

### 扩展功能
- 支持可变周期时长
- 集成实时交通数据
- 添加多路口协调优化

这个编程实现文档提供了问题二的完整技术解决方案，包括相位设计、延误模型、多种优化算法、性能评估和可视化等所有编程环节。
