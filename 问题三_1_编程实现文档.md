# 问题三：绕路车与车位需求统计 - 编程实现文档

## 📋 **编程任务概述**

### 核心任务
- 识别和统计绕路车辆数量
- 分析绕路车辆的时空分布特征
- 计算停车位需求量
- 建立车位配置优化模型
- 生成绕路车分析报告和车位需求建议

### 技术要求
- 实现基于时间窗口的绕路车识别算法
- 开发停车需求预测模型
- 建立车位利用率分析系统
- 生成可视化分析图表和决策支持报告

## 💻 **核心算法实现**

### 1. 绕路车识别算法

```python
class DetourVehicleDetector:
    def __init__(self, time_threshold=30, distance_threshold=500):
        """
        初始化绕路车检测器
        
        Args:
            time_threshold: 时间阈值(分钟)，同一车辆在此时间内多次通过视为绕路
            distance_threshold: 距离阈值(米)，用于判断是否为同一路段
        """
        self.time_threshold = time_threshold
        self.distance_threshold = distance_threshold
        self.detour_patterns = {
            'U型绕路': ['东→西', '西→东'],
            '环形绕路': ['东→南→西→北'],
            '往返绕路': ['南→北', '北→南'],
            '多次通过': '同一方向多次通过'
        }
    
    def identify_detour_vehicles(self, traffic_data):
        """识别绕路车辆主算法"""
        print("开始识别绕路车辆...")
        
        # 按车牌号分组
        vehicle_groups = traffic_data.groupby('车牌号')
        detour_vehicles = []
        
        for plate, group in vehicle_groups:
            if len(group) >= 2:  # 至少出现2次才可能是绕路
                detour_info = self.analyze_vehicle_pattern(plate, group)
                if detour_info:
                    detour_vehicles.append(detour_info)
        
        print(f"识别到 {len(detour_vehicles)} 辆绕路车")
        return detour_vehicles
    
    def analyze_vehicle_pattern(self, plate, vehicle_records):
        """分析单个车辆的行驶模式"""
        # 按时间排序
        records = vehicle_records.sort_values('时间')
        
        # 检查时间间隔
        time_diffs = []
        for i in range(1, len(records)):
            time_diff = (records.iloc[i]['时间'] - records.iloc[i-1]['时间']).total_seconds() / 60
            time_diffs.append(time_diff)
        
        # 检查是否在时间阈值内
        if any(diff <= self.time_threshold for diff in time_diffs):
            return self.classify_detour_pattern(plate, records)
        
        return None
    
    def classify_detour_pattern(self, plate, records):
        """分类绕路模式"""
        directions = records['方向描述'].tolist()
        movements = records['行驶方向'].tolist()
        times = records['时间'].tolist()
        
        # 分析绕路类型
        detour_type = self.determine_detour_type(directions, movements)
        
        # 计算绕路特征
        total_passes = len(records)
        time_span = (times[-1] - times[0]).total_seconds() / 60  # 分钟
        
        return {
            '车牌号': plate,
            '绕路类型': detour_type,
            '通过次数': total_passes,
            '时间跨度': time_span,
            '首次通过': times[0],
            '最后通过': times[-1],
            '方向序列': directions,
            '行驶方向序列': movements,
            '可能停车': self.estimate_parking_probability(records)
        }
    
    def determine_detour_type(self, directions, movements):
        """确定绕路类型"""
        direction_set = set(directions)
        
        # U型绕路：相反方向
        if len(direction_set) == 2:
            if ('由东向西' in directions and '由西向东' in directions) or \
               ('由南向北' in directions and '由北向南' in directions):
                return 'U型绕路'
        
        # 环形绕路：多个方向
        if len(direction_set) >= 3:
            return '环形绕路'
        
        # 多次通过：同一方向多次
        if len(direction_set) == 1 and len(directions) > 1:
            return '多次通过'
        
        return '其他绕路'
    
    def estimate_parking_probability(self, records):
        """估算停车概率"""
        if len(records) < 2:
            return 0.0
        
        # 基于时间间隔估算停车概率
        time_gaps = []
        for i in range(1, len(records)):
            gap = (records.iloc[i]['时间'] - records.iloc[i-1]['时间']).total_seconds() / 60
            time_gaps.append(gap)
        
        # 如果有较长的时间间隔，认为可能停车
        max_gap = max(time_gaps)
        if max_gap > 10:  # 超过10分钟认为可能停车
            return min(1.0, max_gap / 60)  # 最大概率为1
        
        return 0.2  # 基础停车概率
```

### 2. 停车需求分析算法

```python
class ParkingDemandAnalyzer:
    def __init__(self):
        """初始化停车需求分析器"""
        self.demand_factors = {
            '绕路车比例': 0.4,      # 绕路车中需要停车的比例
            '平均停车时长': 45,     # 分钟
            '高峰系数': 1.5,        # 高峰时段系数
            '安全系数': 1.2         # 安全余量系数
        }
    
    def analyze_parking_demand(self, detour_vehicles, traffic_data):
        """分析停车需求"""
        print("分析停车需求...")
        
        # 基础需求分析
        basic_demand = self.calculate_basic_demand(detour_vehicles)
        
        # 时段需求分析
        hourly_demand = self.calculate_hourly_demand(detour_vehicles)
        
        # 高峰需求分析
        peak_demand = self.calculate_peak_demand(hourly_demand)
        
        # 车位配置建议
        parking_recommendation = self.generate_parking_recommendation(peak_demand)
        
        return {
            '基础需求': basic_demand,
            '小时需求': hourly_demand,
            '高峰需求': peak_demand,
            '配置建议': parking_recommendation
        }
    
    def calculate_basic_demand(self, detour_vehicles):
        """计算基础停车需求"""
        total_detours = len(detour_vehicles)
        
        # 估算需要停车的车辆数
        parking_vehicles = 0
        for vehicle in detour_vehicles:
            parking_prob = vehicle.get('可能停车', 0.2)
            parking_vehicles += parking_prob
        
        # 基础统计
        basic_stats = {
            '总绕路车数': total_detours,
            '估算停车车辆': int(parking_vehicles),
            '停车比例': parking_vehicles / total_detours if total_detours > 0 else 0,
            '绕路类型分布': self.analyze_detour_types(detour_vehicles)
        }
        
        return basic_stats
    
    def calculate_hourly_demand(self, detour_vehicles):
        """计算小时停车需求"""
        hourly_stats = {}
        
        for hour in range(24):
            hour_vehicles = []
            for vehicle in detour_vehicles:
                first_pass = vehicle['首次通过']
                if first_pass.hour == hour:
                    hour_vehicles.append(vehicle)
            
            # 计算该小时的停车需求
            hour_demand = 0
            for vehicle in hour_vehicles:
                parking_prob = vehicle.get('可能停车', 0.2)
                hour_demand += parking_prob
            
            hourly_stats[hour] = {
                '绕路车数': len(hour_vehicles),
                '停车需求': int(hour_demand),
                '需求强度': hour_demand / len(hour_vehicles) if hour_vehicles else 0
            }
        
        return hourly_stats
    
    def calculate_peak_demand(self, hourly_demand):
        """计算高峰停车需求"""
        # 找出需求最高的时段
        max_demand = 0
        peak_hours = []
        
        for hour, stats in hourly_demand.items():
            demand = stats['停车需求']
            if demand > max_demand:
                max_demand = demand
                peak_hours = [hour]
            elif demand == max_demand and demand > 0:
                peak_hours.append(hour)
        
        # 计算高峰需求特征
        peak_stats = {
            '最大小时需求': max_demand,
            '高峰时段': peak_hours,
            '高峰持续时长': len(peak_hours),
            '日均需求': sum(stats['停车需求'] for stats in hourly_demand.values()) / 24
        }
        
        return peak_stats
    
    def generate_parking_recommendation(self, peak_demand):
        """生成停车位配置建议"""
        max_demand = peak_demand['最大小时需求']
        
        # 应用各种系数
        recommended_spaces = max_demand * self.demand_factors['高峰系数'] * self.demand_factors['安全系数']
        
        # 分级建议
        recommendations = {
            '最小配置': int(max_demand),
            '推荐配置': int(recommended_spaces),
            '最大配置': int(recommended_spaces * 1.3),
            '配置说明': {
                '最小配置': '仅满足高峰时段基本需求',
                '推荐配置': '考虑高峰系数和安全余量',
                '最大配置': '考虑未来增长和特殊情况'
            }
        }
        
        return recommendations
    
    def analyze_detour_types(self, detour_vehicles):
        """分析绕路类型分布"""
        type_counts = {}
        for vehicle in detour_vehicles:
            detour_type = vehicle['绕路类型']
            type_counts[detour_type] = type_counts.get(detour_type, 0) + 1
        
        return type_counts
```

### 3. 车位利用率分析算法

```python
class ParkingUtilizationAnalyzer:
    def __init__(self):
        """初始化车位利用率分析器"""
        self.utilization_models = {
            '工作日': {'上午': 0.3, '下午': 0.6, '晚间': 0.4, '夜间': 0.1},
            '周末': {'上午': 0.4, '下午': 0.7, '晚间': 0.5, '夜间': 0.2},
            '黄金周': {'上午': 0.6, '下午': 0.8, '晚间': 0.7, '夜间': 0.3}
        }
    
    def analyze_utilization_patterns(self, detour_vehicles, parking_demand):
        """分析车位利用率模式"""
        print("分析车位利用率...")
        
        # 时段利用率分析
        period_utilization = self.calculate_period_utilization(detour_vehicles)
        
        # 日期类型利用率分析
        date_type_utilization = self.calculate_date_type_utilization(detour_vehicles)
        
        # 利用率预测模型
        utilization_forecast = self.forecast_utilization(period_utilization, date_type_utilization)
        
        # 优化建议
        optimization_suggestions = self.generate_optimization_suggestions(utilization_forecast)
        
        return {
            '时段利用率': period_utilization,
            '日期类型利用率': date_type_utilization,
            '利用率预测': utilization_forecast,
            '优化建议': optimization_suggestions
        }
    
    def calculate_period_utilization(self, detour_vehicles):
        """计算时段利用率"""
        period_stats = {
            '早高峰': {'车辆数': 0, '停车需求': 0},
            '午间晚高峰': {'车辆数': 0, '停车需求': 0},
            '夜间时段': {'车辆数': 0, '停车需求': 0},
            '凌晨时段': {'车辆数': 0, '停车需求': 0}
        }
        
        for vehicle in detour_vehicles:
            first_pass = vehicle['首次通过']
            hour = first_pass.hour
            
            # 确定时段
            if 6 <= hour < 12:
                period = '早高峰'
            elif 12 <= hour < 19:
                period = '午间晚高峰'
            elif 19 <= hour < 24:
                period = '夜间时段'
            else:
                period = '凌晨时段'
            
            period_stats[period]['车辆数'] += 1
            period_stats[period]['停车需求'] += vehicle.get('可能停车', 0.2)
        
        # 计算利用率
        for period in period_stats:
            vehicles = period_stats[period]['车辆数']
            demand = period_stats[period]['停车需求']
            period_stats[period]['利用率'] = demand / vehicles if vehicles > 0 else 0
        
        return period_stats
    
    def calculate_date_type_utilization(self, detour_vehicles):
        """计算日期类型利用率"""
        date_type_stats = {
            '工作日': {'车辆数': 0, '停车需求': 0},
            '周末': {'车辆数': 0, '停车需求': 0},
            '黄金周': {'车辆数': 0, '停车需求': 0}
        }
        
        for vehicle in detour_vehicles:
            first_pass = vehicle['首次通过']
            
            # 确定日期类型（简化判断）
            weekday = first_pass.weekday()
            if first_pass.month == 5 and 1 <= first_pass.day <= 5:
                date_type = '黄金周'
            elif weekday >= 5:
                date_type = '周末'
            else:
                date_type = '工作日'
            
            date_type_stats[date_type]['车辆数'] += 1
            date_type_stats[date_type]['停车需求'] += vehicle.get('可能停车', 0.2)
        
        # 计算利用率
        for date_type in date_type_stats:
            vehicles = date_type_stats[date_type]['车辆数']
            demand = date_type_stats[date_type]['停车需求']
            date_type_stats[date_type]['利用率'] = demand / vehicles if vehicles > 0 else 0
        
        return date_type_stats
    
    def forecast_utilization(self, period_util, date_type_util):
        """预测利用率"""
        forecast = {}
        
        for date_type, date_data in date_type_util.items():
            forecast[date_type] = {}
            base_utilization = date_data['利用率']
            
            for period, period_data in period_util.items():
                period_factor = period_data['利用率']
                
                # 综合预测利用率
                predicted_util = (base_utilization + period_factor) / 2
                
                # 应用经验模型调整
                if date_type in self.utilization_models:
                    period_key = self.map_period_to_key(period)
                    if period_key in self.utilization_models[date_type]:
                        model_util = self.utilization_models[date_type][period_key]
                        predicted_util = (predicted_util + model_util) / 2
                
                forecast[date_type][period] = min(1.0, predicted_util)
        
        return forecast
    
    def map_period_to_key(self, period):
        """映射时段到模型键"""
        mapping = {
            '早高峰': '上午',
            '午间晚高峰': '下午',
            '夜间时段': '晚间',
            '凌晨时段': '夜间'
        }
        return mapping.get(period, '上午')
    
    def generate_optimization_suggestions(self, utilization_forecast):
        """生成优化建议"""
        suggestions = []
        
        # 分析利用率模式
        for date_type, periods in utilization_forecast.items():
            max_util = max(periods.values())
            min_util = min(periods.values())
            
            if max_util - min_util > 0.3:  # 利用率差异较大
                suggestions.append({
                    '类型': '时段调节',
                    '日期类型': date_type,
                    '建议': f'在{date_type}实施分时段收费，平衡利用率',
                    '预期效果': '提高整体利用效率'
                })
            
            if max_util > 0.8:  # 高利用率
                suggestions.append({
                    '类型': '容量扩充',
                    '日期类型': date_type,
                    '建议': f'{date_type}需要增加停车位',
                    '预期效果': '缓解停车压力'
                })
            
            if min_util < 0.2:  # 低利用率
                suggestions.append({
                    '类型': '需求引导',
                    '日期类型': date_type,
                    '建议': f'{date_type}可以引导更多车辆停车',
                    '预期效果': '提高经济效益'
                })
        
        return suggestions
```

### 4. 空间布局优化算法

```python
class ParkingLayoutOptimizer:
    def __init__(self):
        """初始化停车布局优化器"""
        self.layout_constraints = {
            '最小车位尺寸': {'长': 5.3, '宽': 2.4},  # 米
            '通道宽度': 6.0,  # 米
            '安全距离': 1.0,  # 米
            '最大步行距离': 200  # 米
        }
    
    def optimize_parking_layout(self, demand_analysis, available_area):
        """优化停车布局"""
        print("优化停车布局...")
        
        # 需求分析
        total_demand = demand_analysis['配置建议']['推荐配置']
        
        # 空间分析
        space_analysis = self.analyze_available_space(available_area)
        
        # 布局设计
        layout_design = self.design_parking_layout(total_demand, space_analysis)
        
        # 效率评估
        efficiency_assessment = self.assess_layout_efficiency(layout_design)
        
        return {
            '需求分析': {'总需求': total_demand},
            '空间分析': space_analysis,
            '布局设计': layout_design,
            '效率评估': efficiency_assessment
        }
    
    def analyze_available_space(self, available_area):
        """分析可用空间"""
        # 假设可用区域信息
        areas = [
            {'名称': '路口东北角', '面积': 1200, '形状': '矩形', '长': 40, '宽': 30},
            {'名称': '路口西南角', '面积': 800, '形状': '矩形', '长': 32, '宽': 25},
            {'名称': '路边停车带', '面积': 600, '形状': '条形', '长': 100, '宽': 6}
        ]
        
        space_analysis = {}
        for area in areas:
            capacity = self.calculate_area_capacity(area)
            space_analysis[area['名称']] = {
                '面积': area['面积'],
                '理论容量': capacity,
                '利用率': 0.85,  # 考虑通道等
                '实际容量': int(capacity * 0.85)
            }
        
        return space_analysis
    
    def calculate_area_capacity(self, area):
        """计算区域停车容量"""
        if area['形状'] == '矩形':
            # 矩形区域容量计算
            length = area['长']
            width = area['宽']
            
            # 考虑车位和通道
            spaces_per_row = int((width - self.layout_constraints['通道宽度']) / 
                               (self.layout_constraints['最小车位尺寸']['宽'] + 1))
            rows = int(length / (self.layout_constraints['最小车位尺寸']['长'] + 1))
            
            return spaces_per_row * rows
        
        elif area['形状'] == '条形':
            # 条形区域（路边停车）
            length = area['长']
            space_length = self.layout_constraints['最小车位尺寸']['长']
            
            return int(length / (space_length + 1))
        
        return 0
    
    def design_parking_layout(self, total_demand, space_analysis):
        """设计停车布局"""
        layout_design = {}
        remaining_demand = total_demand
        
        # 按优先级分配
        priority_areas = ['路口东北角', '路口西南角', '路边停车带']
        
        for area_name in priority_areas:
            if area_name in space_analysis and remaining_demand > 0:
                area_capacity = space_analysis[area_name]['实际容量']
                allocated = min(remaining_demand, area_capacity)
                
                layout_design[area_name] = {
                    '分配车位': allocated,
                    '利用率': allocated / area_capacity if area_capacity > 0 else 0,
                    '布局方式': self.determine_layout_pattern(area_name, allocated)
                }
                
                remaining_demand -= allocated
        
        # 总结
        layout_design['总结'] = {
            '总需求': total_demand,
            '已分配': total_demand - remaining_demand,
            '未满足': remaining_demand,
            '满足率': (total_demand - remaining_demand) / total_demand if total_demand > 0 else 0
        }
        
        return layout_design
    
    def determine_layout_pattern(self, area_name, spaces):
        """确定布局模式"""
        patterns = {
            '路口东北角': f'垂直停车，{spaces}个车位，双排布局',
            '路口西南角': f'斜向停车，{spaces}个车位，单排布局',
            '路边停车带': f'平行停车，{spaces}个车位，单排布局'
        }
        
        return patterns.get(area_name, f'标准布局，{spaces}个车位')
    
    def assess_layout_efficiency(self, layout_design):
        """评估布局效率"""
        total_allocated = layout_design['总结']['已分配']
        satisfaction_rate = layout_design['总结']['满足率']
        
        # 计算各项效率指标
        efficiency_metrics = {
            '空间利用率': satisfaction_rate,
            '便民程度': self.calculate_convenience_score(layout_design),
            '管理难度': self.calculate_management_difficulty(layout_design),
            '经济效益': self.calculate_economic_benefit(total_allocated)
        }
        
        # 综合评分
        weights = {'空间利用率': 0.3, '便民程度': 0.3, '管理难度': 0.2, '经济效益': 0.2}
        overall_score = sum(score * weights[metric] for metric, score in efficiency_metrics.items())
        
        efficiency_metrics['综合评分'] = overall_score
        
        return efficiency_metrics
    
    def calculate_convenience_score(self, layout_design):
        """计算便民程度评分"""
        # 基于停车位分布的便民性评分
        area_scores = {
            '路口东北角': 0.9,  # 位置较好
            '路口西南角': 0.8,  # 位置一般
            '路边停车带': 0.6   # 位置较远
        }
        
        total_spaces = 0
        weighted_score = 0
        
        for area_name, area_data in layout_design.items():
            if area_name != '总结' and '分配车位' in area_data:
                spaces = area_data['分配车位']
                score = area_scores.get(area_name, 0.5)
                
                weighted_score += spaces * score
                total_spaces += spaces
        
        return weighted_score / total_spaces if total_spaces > 0 else 0
    
    def calculate_management_difficulty(self, layout_design):
        """计算管理难度评分（越高越容易管理）"""
        # 基于布局复杂度的管理难度评分
        difficulty_scores = {
            '路口东北角': 0.8,  # 相对容易管理
            '路口西南角': 0.7,  # 中等难度
            '路边停车带': 0.9   # 容易管理
        }
        
        total_spaces = 0
        weighted_score = 0
        
        for area_name, area_data in layout_design.items():
            if area_name != '总结' and '分配车位' in area_data:
                spaces = area_data['分配车位']
                score = difficulty_scores.get(area_name, 0.5)
                
                weighted_score += spaces * score
                total_spaces += spaces
        
        return weighted_score / total_spaces if total_spaces > 0 else 0
    
    def calculate_economic_benefit(self, total_spaces):
        """计算经济效益评分"""
        # 基于停车位数量的经济效益评分
        if total_spaces >= 200:
            return 0.9
        elif total_spaces >= 150:
            return 0.8
        elif total_spaces >= 100:
            return 0.7
        elif total_spaces >= 50:
            return 0.6
        else:
            return 0.5
```

### 5. 可视化与报告生成

```python
import matplotlib.pyplot as plt
import numpy as np

class DetourVisualization:
    def __init__(self):
        """初始化可视化器"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def create_comprehensive_analysis_charts(self, detour_analysis, demand_analysis, layout_optimization):
        """创建综合分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 绕路车类型分布
        self.plot_detour_types(detour_analysis, axes[0, 0])
        
        # 2. 停车需求时段分布
        self.plot_hourly_demand(demand_analysis, axes[0, 1])
        
        # 3. 车位利用率预测
        self.plot_utilization_forecast(demand_analysis, axes[1, 0])
        
        # 4. 布局效率评估
        self.plot_layout_efficiency(layout_optimization, axes[1, 1])
        
        plt.tight_layout()
        plt.savefig('问题三_绕路车分析.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_detour_types(self, detour_analysis, ax):
        """绘制绕路车类型分布"""
        basic_demand = detour_analysis['基础需求']
        type_dist = basic_demand['绕路类型分布']
        
        types = list(type_dist.keys())
        counts = list(type_dist.values())
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        wedges, texts, autotexts = ax.pie(counts, labels=types, autopct='%1.1f%%',
                                         colors=colors, startangle=90)
        ax.set_title('绕路车类型分布', fontsize=14, fontweight='bold')
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    def plot_hourly_demand(self, demand_analysis, ax):
        """绘制小时停车需求"""
        hourly_demand = demand_analysis['小时需求']
        
        hours = list(range(24))
        demands = [hourly_demand.get(h, {}).get('停车需求', 0) for h in hours]
        
        bars = ax.bar(hours, demands, color='steelblue', alpha=0.7)
        ax.set_title('24小时停车需求分布', fontsize=14, fontweight='bold')
        ax.set_xlabel('小时', fontsize=12)
        ax.set_ylabel('停车需求量', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 标注高峰时段
        peak_demand = demand_analysis['高峰需求']
        peak_hours = peak_demand['高峰时段']
        for hour in peak_hours:
            if hour < len(bars):
                bars[hour].set_color('red')
                bars[hour].set_alpha(0.8)
    
    def plot_utilization_forecast(self, demand_analysis, ax):
        """绘制利用率预测"""
        # 这里使用模拟数据，实际应该从demand_analysis中获取
        periods = ['早高峰', '午间晚高峰', '夜间时段', '凌晨时段']
        workday = [0.3, 0.6, 0.4, 0.1]
        weekend = [0.4, 0.7, 0.5, 0.2]
        holiday = [0.6, 0.8, 0.7, 0.3]
        
        x = np.arange(len(periods))
        width = 0.25
        
        bars1 = ax.bar(x - width, workday, width, label='工作日', alpha=0.8)
        bars2 = ax.bar(x, weekend, width, label='周末', alpha=0.8)
        bars3 = ax.bar(x + width, holiday, width, label='黄金周', alpha=0.8)
        
        ax.set_xlabel('时段')
        ax.set_ylabel('利用率')
        ax.set_title('车位利用率预测')
        ax.set_xticks(x)
        ax.set_xticklabels(periods)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def plot_layout_efficiency(self, layout_optimization, ax):
        """绘制布局效率评估"""
        efficiency = layout_optimization['效率评估']
        
        metrics = ['空间利用率', '便民程度', '管理难度', '经济效益']
        scores = [efficiency.get(metric, 0) for metric in metrics]
        
        # 雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        scores += scores[:1]  # 闭合
        angles += angles[:1]
        
        ax.plot(angles, scores, 'o-', linewidth=2, label='当前方案')
        ax.fill(angles, scores, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('停车布局效率评估')
        ax.grid(True)
```

### 6. 主程序集成

```python
def main():
    """问题三主程序"""
    print("问题三：绕路车与车位需求统计")
    print("="*50)
    
    try:
        # 1. 初始化组件
        detector = DetourVehicleDetector()
        demand_analyzer = ParkingDemandAnalyzer()
        utilization_analyzer = ParkingUtilizationAnalyzer()
        layout_optimizer = ParkingLayoutOptimizer()
        visualizer = DetourVisualization()
        
        # 2. 加载交通数据
        print("加载交通数据...")
        traffic_data = load_traffic_data()
        
        # 3. 识别绕路车辆
        print("识别绕路车辆...")
        detour_vehicles = detector.identify_detour_vehicles(traffic_data)
        
        # 4. 分析停车需求
        print("分析停车需求...")
        demand_analysis = demand_analyzer.analyze_parking_demand(detour_vehicles, traffic_data)
        
        # 5. 分析利用率
        print("分析车位利用率...")
        utilization_analysis = utilization_analyzer.analyze_utilization_patterns(
            detour_vehicles, demand_analysis
        )
        
        # 6. 优化布局
        print("优化停车布局...")
        layout_optimization = layout_optimizer.optimize_parking_layout(
            demand_analysis, available_area=None
        )
        
        # 7. 生成可视化
        print("生成可视化图表...")
        visualizer.create_comprehensive_analysis_charts(
            {'基础需求': demand_analysis['基础需求']},
            demand_analysis,
            layout_optimization
        )
        
        # 8. 生成报告
        print("生成分析报告...")
        generate_detour_analysis_report(
            detour_vehicles, demand_analysis, utilization_analysis, layout_optimization
        )
        
        print("✓ 问题三分析完成！")
        return detour_vehicles, demand_analysis, layout_optimization
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    main()
```

这个编程实现文档提供了问题三的完整技术解决方案，包括绕路车识别、停车需求分析、利用率分析、布局优化和可视化等所有编程环节。
