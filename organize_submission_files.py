#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数模大赛文件整理脚本
将所有相关文件整理到提交文件夹中
"""

import os
import shutil
from datetime import datetime

class SubmissionOrganizer:
    def __init__(self):
        """初始化文件整理器"""
        self.submission_folder = "2024数模大赛E题提交材料"
        self.source_dirs = {
            'excel_data': 'excel_data',
            'csv_data': 'csv_data', 
            'results': 'results',
            'visualizations': 'visualizations'
        }
        
        # 定义文件分类
        self.file_categories = {
            '核心论文': {
                'folder': '',
                'files': ['2024数模大赛E题论文.docx', '附件2.csv']
            },
            '数据处理成果': {
                'folder': '数据处理成果',
                'subfolders': {
                    'Excel数据文件': 'excel_data',
                    'CSV数据文件': 'csv_data'
                }
            },
            '分析结果': {
                'folder': '分析结果',
                'files': [
                    'results/综合分析报告.txt',
                    'results/问题一分析报告.txt', 
                    'results/问题二分析报告.txt',
                    'results/问题三分析报告.txt',
                    'results/问题四分析报告.txt',
                    'results/overall_summary.json',
                    'results/may_2_period_3_analysis.json'
                ],
                'patterns': ['results/统计信息_第*.json']
            },
            '可视化图表': {
                'folder': '可视化图表',
                'files': [
                    'visualizations/问题一_车流量分析.png',
                    'visualizations/问题二_信号灯优化.png',
                    'visualizations/问题三_绕路车分析.png',
                    'visualizations/问题四_管理成效对比.png'
                ]
            },
            '程序代码': {
                'folder': '程序代码',
                'files': [
                    'simple_data_processor.py',
                    'excel_converter.py',
                    'visualization_analyzer.py',
                    'paper_generator.py'
                ]
            },
            '技术文档': {
                'folder': '技术文档',
                'files': [
                    'problem1_technical_analysis.md',
                    'problem2_technical_analysis.md',
                    'problem3_technical_analysis.md',
                    'problem4_technical_analysis.md',
                    '技术文档总览.md'
                ]
            },
            '项目说明': {
                'folder': '项目说明',
                'files': [
                    '项目完成总结.md',
                    'results/数据完整性检查报告.txt'
                ]
            }
        }
    
    def create_folder_structure(self):
        """创建提交文件夹结构"""
        print("创建提交文件夹结构...")
        
        # 创建主文件夹
        if os.path.exists(self.submission_folder):
            shutil.rmtree(self.submission_folder)
        os.makedirs(self.submission_folder)
        
        # 创建子文件夹
        for category, info in self.file_categories.items():
            if 'folder' in info and info['folder']:
                folder_path = os.path.join(self.submission_folder, info['folder'])
                os.makedirs(folder_path, exist_ok=True)
                print(f"✓ 创建文件夹: {info['folder']}")
                
                # 创建子子文件夹
                if 'subfolders' in info:
                    for subfolder_name, _ in info['subfolders'].items():
                        subfolder_path = os.path.join(folder_path, subfolder_name)
                        os.makedirs(subfolder_path, exist_ok=True)
                        print(f"  ✓ 创建子文件夹: {subfolder_name}")
    
    def copy_files(self):
        """复制文件到提交文件夹"""
        print("\n开始复制文件...")
        
        total_files = 0
        copied_files = 0
        
        for category, info in self.file_categories.items():
            print(f"\n处理类别: {category}")
            
            # 处理直接文件列表
            if 'files' in info:
                for file_path in info['files']:
                    if self.copy_single_file(file_path, category, info):
                        copied_files += 1
                    total_files += 1
            
            # 处理文件模式匹配
            if 'patterns' in info:
                for pattern in info['patterns']:
                    matched_files = self.find_files_by_pattern(pattern)
                    for file_path in matched_files:
                        if self.copy_single_file(file_path, category, info):
                            copied_files += 1
                        total_files += 1
            
            # 处理子文件夹
            if 'subfolders' in info:
                for subfolder_name, source_dir in info['subfolders'].items():
                    copied, total = self.copy_directory_files(source_dir, category, info, subfolder_name)
                    copied_files += copied
                    total_files += total
        
        print(f"\n文件复制完成: {copied_files}/{total_files} 个文件成功复制")
        return copied_files, total_files
    
    def copy_single_file(self, source_path, category, info):
        """复制单个文件"""
        if not os.path.exists(source_path):
            print(f"  ✗ 文件不存在: {source_path}")
            return False
        
        # 确定目标路径
        if info.get('folder'):
            target_dir = os.path.join(self.submission_folder, info['folder'])
        else:
            target_dir = self.submission_folder
        
        filename = os.path.basename(source_path)
        target_path = os.path.join(target_dir, filename)
        
        try:
            shutil.copy2(source_path, target_path)
            file_size = os.path.getsize(source_path)
            print(f"  ✓ {filename} ({self.format_size(file_size)})")
            return True
        except Exception as e:
            print(f"  ✗ 复制失败 {filename}: {e}")
            return False
    
    def copy_directory_files(self, source_dir, category, info, subfolder_name):
        """复制目录中的所有文件"""
        if not os.path.exists(source_dir):
            print(f"  ✗ 目录不存在: {source_dir}")
            return 0, 0
        
        target_dir = os.path.join(self.submission_folder, info['folder'], subfolder_name)
        
        copied_files = 0
        total_files = 0
        
        for filename in os.listdir(source_dir):
            source_path = os.path.join(source_dir, filename)
            if os.path.isfile(source_path):
                target_path = os.path.join(target_dir, filename)
                
                try:
                    shutil.copy2(source_path, target_path)
                    file_size = os.path.getsize(source_path)
                    print(f"  ✓ {filename} ({self.format_size(file_size)})")
                    copied_files += 1
                except Exception as e:
                    print(f"  ✗ 复制失败 {filename}: {e}")
                
                total_files += 1
        
        return copied_files, total_files
    
    def find_files_by_pattern(self, pattern):
        """根据模式查找文件"""
        import glob
        return glob.glob(pattern)
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def generate_file_list(self):
        """生成文件清单"""
        print("\n生成文件清单...")
        
        file_list = []
        file_list.append("# 2024数模大赛E题提交材料清单")
        file_list.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        file_list.append("")
        
        total_size = 0
        total_count = 0
        
        for root, dirs, files in os.walk(self.submission_folder):
            if files:
                # 计算相对路径
                rel_path = os.path.relpath(root, self.submission_folder)
                if rel_path == '.':
                    folder_name = "根目录"
                else:
                    folder_name = rel_path
                
                file_list.append(f"## {folder_name}")
                file_list.append("")
                
                folder_size = 0
                for file in sorted(files):
                    if file.endswith('.md') and 'file_list' in file:
                        continue  # 跳过清单文件本身
                    
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    folder_size += file_size
                    total_size += file_size
                    total_count += 1
                    
                    file_list.append(f"- {file} ({self.format_size(file_size)})")
                
                file_list.append(f"\n**小计**: {len([f for f in files if not (f.endswith('.md') and 'file_list' in f)])} 个文件, {self.format_size(folder_size)}")
                file_list.append("")
        
        file_list.append("---")
        file_list.append(f"**总计**: {total_count} 个文件, {self.format_size(total_size)}")
        
        # 保存文件清单
        list_file = os.path.join(self.submission_folder, "提交材料清单.md")
        with open(list_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(file_list))
        
        print(f"✓ 文件清单已生成: 提交材料清单.md")
        print(f"✓ 总计: {total_count} 个文件, {self.format_size(total_size)}")
    
    def create_readme(self):
        """创建README文件"""
        readme_content = """# 2024数模大赛E题提交材料

## 项目概述
小镇景区实施临时交通管制措施分析

## 文件结构说明

### 📄 核心文件
- `2024数模大赛E题论文.docx` - 主要论文
- `附件2.csv` - 原始数据文件

### 📁 主要文件夹
- `数据处理成果/` - Excel和CSV数据文件
- `分析结果/` - 分析报告和JSON数据
- `可视化图表/` - 专业图表文件
- `程序代码/` - Python程序源码
- `技术文档/` - 详细技术分析文档
- `项目说明/` - 项目总结和说明

## 运行说明
1. 主要程序: `程序代码/simple_data_processor.py`
2. 数据转换: `程序代码/excel_converter.py`
3. 可视化分析: `程序代码/visualization_analyzer.py`
4. 论文生成: `程序代码/paper_generator.py`

## 技术特色
- 完整的四个问题解决方案
- 76,888条数据的完整处理
- 多算法融合的优化模型
- 专业的可视化分析
- 详实的技术文档

## 联系信息
队伍: 数模精英队
完成时间: 2024年
"""
        
        readme_file = os.path.join(self.submission_folder, "README.md")
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✓ README文件已生成")
    
    def organize_all(self):
        """执行完整的文件整理流程"""
        print("="*60)
        print("2024数模大赛E题 - 文件整理器")
        print("="*60)
        
        try:
            # 1. 创建文件夹结构
            self.create_folder_structure()
            
            # 2. 复制文件
            copied, total = self.copy_files()
            
            # 3. 生成文件清单
            self.generate_file_list()
            
            # 4. 创建README
            self.create_readme()
            
            print("\n" + "="*60)
            print("文件整理完成！")
            print("="*60)
            print(f"✓ 提交文件夹: {self.submission_folder}")
            print(f"✓ 成功复制: {copied}/{total} 个文件")
            print(f"✓ 文件夹结构完整")
            print(f"✓ 清单和说明文件已生成")
            print("="*60)
            
            return True
            
        except Exception as e:
            print(f"文件整理失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    organizer = SubmissionOrganizer()
    success = organizer.organize_all()
    
    if success:
        print("\n🎉 所有文件已整理完毕，可以用于数模大赛提交！")
    else:
        print("\n❌ 文件整理过程中出现错误，请检查。")

if __name__ == "__main__":
    main()
