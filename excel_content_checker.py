#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Excel文件内容检查器
检查Excel文件的工作表和内容质量
"""

import os
import pandas as pd

def check_excel_content():
    """检查Excel文件内容"""
    print("="*60)
    print("Excel文件内容详细检查")
    print("="*60)
    
    excel_dir = 'excel_data'
    
    # 检查第一个Excel文件的详细内容
    first_excel = os.path.join(excel_dir, '交通数据_第01部分.xlsx')
    
    if os.path.exists(first_excel):
        print(f"\n检查文件: {first_excel}")
        
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(first_excel)
            sheet_names = excel_file.sheet_names
            print(f"工作表数量: {len(sheet_names)}")
            print(f"工作表名称: {sheet_names}")
            
            # 检查每个工作表
            for sheet_name in sheet_names:
                print(f"\n--- 工作表: {sheet_name} ---")
                try:
                    df = pd.read_excel(first_excel, sheet_name=sheet_name)
                    print(f"数据行数: {len(df)}")
                    print(f"数据列数: {len(df.columns)}")
                    print(f"列名: {list(df.columns)}")
                    
                    if len(df) > 0:
                        print("前3行数据:")
                        print(df.head(3).to_string())
                    
                except Exception as e:
                    print(f"读取工作表失败: {e}")
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
    
    # 检查总体摘要Excel文件
    summary_excel = os.path.join(excel_dir, '数据分析总体摘要.xlsx')
    
    if os.path.exists(summary_excel):
        print(f"\n" + "="*60)
        print(f"检查总体摘要文件: {summary_excel}")
        print("="*60)
        
        try:
            excel_file = pd.ExcelFile(summary_excel)
            sheet_names = excel_file.sheet_names
            print(f"工作表数量: {len(sheet_names)}")
            print(f"工作表名称: {sheet_names}")
            
            # 检查数据概况工作表
            if '数据概况' in sheet_names:
                print(f"\n--- 数据概况工作表 ---")
                df = pd.read_excel(summary_excel, sheet_name='数据概况')
                print(f"数据行数: {len(df)}")
                print("内容:")
                print(df.to_string(index=False))
            
            # 检查方向分布工作表
            if '方向分布统计' in sheet_names:
                print(f"\n--- 方向分布统计工作表 ---")
                df = pd.read_excel(summary_excel, sheet_name='方向分布统计')
                print(f"数据行数: {len(df)}")
                print("内容:")
                print(df.to_string(index=False))
            
        except Exception as e:
            print(f"读取总体摘要Excel失败: {e}")

def check_csv_sample():
    """检查CSV文件样本内容"""
    print(f"\n" + "="*60)
    print("CSV文件内容样本检查")
    print("="*60)
    
    csv_dir = 'csv_data'
    first_csv = os.path.join(csv_dir, '交通数据_第01部分.csv')
    
    if os.path.exists(first_csv):
        try:
            df = pd.read_csv(first_csv, encoding='utf-8')
            print(f"文件: {first_csv}")
            print(f"数据行数: {len(df)}")
            print(f"数据列数: {len(df.columns)}")
            print(f"列名: {list(df.columns)}")
            
            print("\n前5行数据:")
            print(df.head().to_string(index=False))
            
            print("\n数据类型:")
            print(df.dtypes)
            
            # 检查数据质量
            print("\n数据质量检查:")
            print(f"缺失值: {df.isnull().sum().sum()}")
            print(f"重复行: {df.duplicated().sum()}")
            
            # 检查关键字段
            if '方向编号' in df.columns:
                print(f"方向编号分布: {df['方向编号'].value_counts().to_dict()}")
            
            if '时段' in df.columns:
                print(f"时段分布: {df['时段'].value_counts().to_dict()}")
            
            if '日期类型' in df.columns:
                print(f"日期类型分布: {df['日期类型'].value_counts().to_dict()}")
                
        except Exception as e:
            print(f"读取CSV文件失败: {e}")

def main():
    """主函数"""
    print("2024年数模大赛E题 - Excel内容检查")
    
    try:
        check_excel_content()
        check_csv_sample()
        
        print(f"\n" + "="*60)
        print("Excel内容检查完成！")
        print("="*60)
        
    except Exception as e:
        print(f"检查过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
