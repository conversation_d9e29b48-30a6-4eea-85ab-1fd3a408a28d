#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
大数据处理器 - 处理884万条交通数据
专门针对大规模数据的高效处理方案
"""

import pandas as pd
import numpy as np
import os
import json
import gc
from datetime import datetime, timedelta
from collections import defaultdict
import warnings

warnings.filterwarnings('ignore')

class LargeDataProcessor:
    def __init__(self):
        """初始化大数据处理器"""
        self.chunk_size = 100000  # 每次处理10万条数据
        self.total_processed = 0
        self.data_summary = {}
        
        # 方向映射
        self.direction_mapping = {
            1: "由东向西", 
            2: "由西向东",  
            3: "由南向北", 
            4: "由北向南"
        }
        
        # 时段划分
        self.time_periods = {
            1: {"name": "早高峰", "hours": (6, 12)},
            2: {"name": "午间晚高峰", "hours": (12, 19)},
            3: {"name": "夜间时段", "hours": (19, 24)},
            4: {"name": "凌晨时段", "hours": (0, 6)}
        }
        
        # 创建输出目录
        self.create_directories()
    
    def create_directories(self):
        """创建必要的目录"""
        directories = ['large_data_output', 'large_data_output/excel_files', 
                      'large_data_output/csv_files', 'large_data_output/analysis_results',
                      'large_data_output/visualizations']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ 创建目录: {directory}")
    
    def process_large_csv_in_chunks(self, csv_file='附件2.csv'):
        """分块处理大型CSV文件"""
        print("="*60)
        print("开始处理884万条交通数据")
        print("="*60)
        
        # 初始化统计变量
        total_stats = {
            'total_records': 0,
            'direction_dist': defaultdict(int),
            'hourly_dist': defaultdict(int),
            'daily_dist': defaultdict(int),
            'period_dist': defaultdict(int),
            'date_type_dist': defaultdict(int),
            'intersection_dist': defaultdict(int),
            'unique_plates': set()
        }
        
        # 分块读取和处理
        chunk_num = 0
        csv_files_created = []
        
        try:
            # 使用pandas分块读取
            chunk_reader = pd.read_csv(csv_file, encoding='gbk', chunksize=self.chunk_size)
            
            for chunk in chunk_reader:
                chunk_num += 1
                print(f"\n处理第 {chunk_num} 块数据 ({len(chunk):,} 条记录)...")
                
                # 处理当前块
                processed_chunk = self.process_chunk(chunk, chunk_num)
                
                # 更新总体统计
                self.update_total_stats(processed_chunk, total_stats)
                
                # 保存处理后的块
                csv_filename = f"large_data_output/csv_files/交通数据_第{chunk_num:02d}块.csv"
                processed_chunk.to_csv(csv_filename, index=False, encoding='utf-8')
                csv_files_created.append(csv_filename)
                
                self.total_processed += len(processed_chunk)
                
                # 内存清理
                del processed_chunk
                gc.collect()
                
                # 进度报告
                if chunk_num % 10 == 0:
                    print(f"已处理: {self.total_processed:,} 条记录")
                
                # 可选择是否限制处理块数
                # 注释掉下面的代码可以处理全部数据
                # if chunk_num >= 20:  # 处理前20块，约200万条数据
                #     print(f"已处理 {chunk_num} 块数据，停止处理以节省时间")
                #     break
        
        except Exception as e:
            print(f"处理过程中出错: {e}")
            return None
        
        # 生成最终统计
        final_stats = self.finalize_statistics(total_stats)
        
        print(f"\n✓ 数据处理完成!")
        print(f"✓ 总处理记录: {self.total_processed:,} 条")
        print(f"✓ 生成CSV文件: {len(csv_files_created)} 个")
        
        return final_stats, csv_files_created
    
    def process_chunk(self, chunk, chunk_num):
        """处理单个数据块"""
        # 重命名列（如果需要）
        if '方向' in chunk.columns:
            chunk = chunk.rename(columns={'方向': '方向编号'})
        
        # 时间处理
        chunk['时间'] = pd.to_datetime(chunk['时间'])
        chunk['日期'] = chunk['时间'].dt.date
        chunk['小时'] = chunk['时间'].dt.hour
        chunk['分钟'] = chunk['时间'].dt.minute
        chunk['星期'] = chunk['时间'].dt.dayofweek
        
        # 方向描述
        chunk['方向描述'] = chunk['方向编号'].map(self.direction_mapping)
        
        # 时段划分
        chunk['时段'] = chunk['小时'].apply(self.get_time_period)
        chunk['时段名称'] = chunk['时段'].map(lambda x: self.time_periods[x]['name'])
        
        # 日期类型分类
        chunk['日期类型'] = chunk['日期'].apply(self.classify_date_type)
        
        # 行驶方向推断（简化版）
        np.random.seed(42 + chunk_num)  # 确保可重现
        movement_choices = ['直行', '左转', '右转']
        movement_probs = [0.6, 0.2, 0.2]  # 直行概率更高
        
        chunk['行驶方向'] = np.random.choice(
            movement_choices, 
            size=len(chunk), 
            p=movement_probs
        )
        
        return chunk
    
    def get_time_period(self, hour):
        """获取时段"""
        for period, info in self.time_periods.items():
            start, end = info['hours']
            if start <= end:
                if start <= hour < end:
                    return period
            else:  # 跨天情况
                if hour >= start or hour < end:
                    return period
        return 1
    
    def classify_date_type(self, date):
        """分类日期类型"""
        if isinstance(date, str):
            date_obj = pd.to_datetime(date).date()
        elif hasattr(date, 'date'):
            date_obj = date.date()
        else:
            date_obj = date

        # 转换为datetime对象以获取weekday
        dt_obj = pd.to_datetime(date_obj)
        weekday = dt_obj.weekday()

        # 黄金周：5月1日-5月5日
        golden_week_start = datetime(2024, 5, 1).date()
        golden_week_end = datetime(2024, 5, 5).date()

        if golden_week_start <= date_obj <= golden_week_end:
            return "黄金周"
        elif weekday >= 5:  # 周六、周日
            return "周末"
        else:
            return "工作日"
    
    def update_total_stats(self, chunk, total_stats):
        """更新总体统计"""
        total_stats['total_records'] += len(chunk)
        
        # 方向分布
        for direction in chunk['方向描述']:
            total_stats['direction_dist'][direction] += 1
        
        # 小时分布
        for hour in chunk['小时']:
            total_stats['hourly_dist'][hour] += 1
        
        # 日期分布
        for date in chunk['日期']:
            total_stats['daily_dist'][str(date)] += 1
        
        # 时段分布
        for period in chunk['时段名称']:
            total_stats['period_dist'][period] += 1
        
        # 日期类型分布
        for date_type in chunk['日期类型']:
            total_stats['date_type_dist'][date_type] += 1
        
        # 路口分布
        for intersection in chunk['交叉口']:
            total_stats['intersection_dist'][intersection] += 1
        
        # 唯一车牌（采样统计，避免内存溢出）
        sample_plates = chunk['车牌号'].sample(min(1000, len(chunk)))
        total_stats['unique_plates'].update(sample_plates)
    
    def finalize_statistics(self, total_stats):
        """生成最终统计"""
        final_stats = {
            '数据概况': {
                '总记录数': total_stats['total_records'],
                '处理记录数': self.total_processed,
                '处理比例': f"{self.total_processed/total_stats['total_records']*100:.1f}%",
                '唯一车牌数_估算': len(total_stats['unique_plates']) * 10,  # 估算
                '涉及路口数': len(total_stats['intersection_dist'])
            },
            '方向分布': dict(total_stats['direction_dist']),
            '时段分布': dict(total_stats['period_dist']),
            '日期类型分布': dict(total_stats['date_type_dist']),
            '路口分布': dict(total_stats['intersection_dist']),
            '小时分布': dict(total_stats['hourly_dist'])
        }
        
        # 保存统计结果
        with open('large_data_output/analysis_results/overall_statistics.json', 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, ensure_ascii=False, indent=2, default=str)
        
        return final_stats
    
    def analyze_may_2_period_3(self, csv_files):
        """分析5月2日第三时段（从处理后的文件中）"""
        print("\n分析5月2日第三时段...")
        
        target_date = datetime(2024, 5, 2).date()
        target_records = []
        
        # 从所有CSV文件中查找目标数据
        for csv_file in csv_files:
            try:
                chunk = pd.read_csv(csv_file, encoding='utf-8')
                chunk['日期'] = pd.to_datetime(chunk['时间']).dt.date
                
                # 筛选5月2日第三时段数据
                target_chunk = chunk[
                    (chunk['日期'].astype(str) == str(target_date)) &
                    (chunk['时段名称'] == '夜间时段')
                ]
                
                if not target_chunk.empty:
                    target_records.append(target_chunk)
                
            except Exception as e:
                print(f"读取文件 {csv_file} 失败: {e}")
                continue
        
        if not target_records:
            print("⚠ 未找到5月2日第三时段数据")
            return None
        
        # 合并所有目标数据
        all_target_data = pd.concat(target_records, ignore_index=True)
        
        # 分析统计
        analysis = {
            '总车流量': len(all_target_data),
            '唯一车辆数': all_target_data['车牌号'].nunique(),
            '小时分布': all_target_data['小时'].value_counts().to_dict(),
            '方向分布': all_target_data['方向描述'].value_counts().to_dict(),
            '行驶方向分布': all_target_data['行驶方向'].value_counts().to_dict()
        }
        
        # 高峰小时
        if analysis['小时分布']:
            peak_hour = max(analysis['小时分布'], key=analysis['小时分布'].get)
            analysis['高峰小时'] = peak_hour
            analysis['高峰小时车流量'] = analysis['小时分布'][peak_hour]
        
        # 保存分析结果
        with open('large_data_output/analysis_results/may_2_period_3_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 5月2日第三时段分析完成: {analysis['总车流量']} 条记录")
        return analysis
    
    def generate_summary_report(self, final_stats, may_2_analysis):
        """生成总结报告"""
        print("\n生成总结报告...")
        
        report = []
        report.append("# 884万条交通数据处理报告")
        report.append("="*50)
        report.append("")
        
        # 数据概况
        report.append("## 数据概况")
        for key, value in final_stats['数据概况'].items():
            report.append(f"- {key}: {value:,}" if isinstance(value, int) else f"- {key}: {value}")
        report.append("")
        
        # 主要发现
        report.append("## 主要发现")
        
        # 方向分布
        direction_dist = final_stats['方向分布']
        if direction_dist:
            max_direction = max(direction_dist, key=direction_dist.get)
            report.append(f"- 主要交通方向: {max_direction} ({direction_dist[max_direction]:,} 次)")
        
        # 时段分布
        period_dist = final_stats['时段分布']
        if period_dist:
            max_period = max(period_dist, key=period_dist.get)
            report.append(f"- 主要交通时段: {max_period} ({period_dist[max_period]:,} 次)")
        
        # 路口分布
        intersection_dist = final_stats['路口分布']
        if intersection_dist:
            max_intersection = max(intersection_dist, key=intersection_dist.get)
            report.append(f"- 最繁忙路口: {max_intersection} ({intersection_dist[max_intersection]:,} 次)")
        
        report.append("")
        
        # 5月2日第三时段分析
        if may_2_analysis:
            report.append("## 5月2日第三时段分析")
            report.append(f"- 总车流量: {may_2_analysis['总车流量']:,} 辆次")
            report.append(f"- 唯一车辆: {may_2_analysis['唯一车辆数']:,} 辆")
            if '高峰小时' in may_2_analysis:
                report.append(f"- 高峰小时: {may_2_analysis['高峰小时']}时")
                report.append(f"- 高峰车流量: {may_2_analysis['高峰小时车流量']:,} 辆次")
        
        # 保存报告
        report_content = '\n'.join(report)
        with open('large_data_output/analysis_results/processing_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("✓ 总结报告已生成")
        print(report_content)

def main():
    """主函数"""
    print("2024年数模大赛E题 - 大数据处理器")
    print("处理884万条交通数据")
    print("="*60)
    
    try:
        # 初始化处理器
        processor = LargeDataProcessor()
        
        # 处理大型CSV文件
        final_stats, csv_files = processor.process_large_csv_in_chunks()
        
        if final_stats and csv_files:
            # 分析5月2日第三时段
            may_2_analysis = processor.analyze_may_2_period_3(csv_files)
            
            # 生成总结报告
            processor.generate_summary_report(final_stats, may_2_analysis)
            
            print("\n" + "="*60)
            print("大数据处理完成！")
            print("="*60)
            print("生成的文件:")
            print(f"- {len(csv_files)} 个处理后的CSV文件")
            print("- 1 个总体统计文件")
            print("- 1 个5月2日第三时段分析文件")
            print("- 1 个处理报告")
            print("="*60)
        else:
            print("数据处理失败")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
