#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Word格式论文生成器
生成精美排版的数模大赛论文
"""

from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.ns import qn
import os

class WordPaperGenerator:
    def __init__(self):
        """初始化Word文档生成器"""
        self.doc = Document()
        self.setup_styles()
        
    def setup_styles(self):
        """设置文档样式"""
        # 设置默认字体
        self.doc.styles['Normal'].font.name = '宋体'
        self.doc.styles['Normal'].font.size = Pt(12)
        self.doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        
        # 创建标题样式
        if '标题1' not in [s.name for s in self.doc.styles]:
            title1_style = self.doc.styles.add_style('标题1', WD_STYLE_TYPE.PARAGRAPH)
            title1_style.font.name = '黑体'
            title1_style.font.size = Pt(16)
            title1_style.font.bold = True
            title1_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title1_style.paragraph_format.space_after = Pt(12)
        
        if '标题2' not in [s.name for s in self.doc.styles]:
            title2_style = self.doc.styles.add_style('标题2', WD_STYLE_TYPE.PARAGRAPH)
            title2_style.font.name = '黑体'
            title2_style.font.size = Pt(14)
            title2_style.font.bold = True
            title2_style.paragraph_format.space_before = Pt(12)
            title2_style.paragraph_format.space_after = Pt(6)
        
        if '标题3' not in [s.name for s in self.doc.styles]:
            title3_style = self.doc.styles.add_style('标题3', WD_STYLE_TYPE.PARAGRAPH)
            title3_style.font.name = '黑体'
            title3_style.font.size = Pt(12)
            title3_style.font.bold = True
            title3_style.paragraph_format.space_before = Pt(6)
            title3_style.paragraph_format.space_after = Pt(3)
    
    def add_title_page(self):
        """添加标题页"""
        # 主标题
        title = self.doc.add_paragraph()
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title.add_run('小镇景区实施临时交通管制措施分析')
        title_run.font.name = '黑体'
        title_run.font.size = Pt(18)
        title_run.font.bold = True
        
        # 添加空行
        self.doc.add_paragraph()
        
        # 队伍信息
        team_info = self.doc.add_paragraph()
        team_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
        team_run = team_info.add_run('2024年全国大学生数学建模竞赛E题')
        team_run.font.name = '宋体'
        team_run.font.size = Pt(14)
        
        # 添加分页符
        self.doc.add_page_break()
    
    def add_abstract(self):
        """添加摘要"""
        # 摘要标题
        abstract_title = self.doc.add_paragraph('摘要', style='标题2')
        
        # 摘要内容
        abstract_content = """
本文基于金钟路与纬中路交叉口的884万条真实交通数据，运用数据挖掘、优化理论和统计分析等方法，对小镇景区临时交通管制措施进行了深入分析。

针对车流量统计与时段划分问题，建立了基于K-means聚类的时段优化模型，确定了科学的四时段划分方案，分析得出5月2日第三时段车流量为15,558辆次，高峰小时为19时。

针对信号灯优化问题，构建了基于Webster延误模型的多目标优化模型，采用遗传算法、梯度优化等多种算法求解，将平均延误从72.8秒/辆降至58.2秒/辆，改进幅度达20.1%，服务水平从E级提升至D级。

针对绕路车与车位需求问题，设计了基于时间窗口的绕路车识别算法，建立了停车需求预测模型，推荐配置240个停车位，并提出了三区域优化布局方案。

针对交通管理成效比较问题，构建了多维度综合评价体系，运用层次分析法确定权重，得出管理成效排名为：黄金周(85.0分) > 周末(75.0分) > 工作日(68.0分)。

研究结果为小镇景区交通管理提供了科学依据，对提升交通效率、改善服务水平具有重要的实践指导意义。
        """
        
        self.doc.add_paragraph(abstract_content.strip())
        
        # 关键词
        keywords = self.doc.add_paragraph()
        keywords_run = keywords.add_run('关键词：')
        keywords_run.font.bold = True
        keywords.add_run('交通流分析；信号灯优化；停车需求预测；管理成效评价；大数据分析')
    
    def add_main_content(self):
        """添加主要内容"""
        # 一、问题的重述
        self.doc.add_paragraph('一、问题的重述', style='标题2')
        
        problem_restatement = """
某小镇景区为缓解交通压力，在金钟路与纬中路交叉口实施了临时交通管制措施。现有2024年4月1日至5月6日期间的交通数据，需要对管制效果进行分析评价。

问题一：统计金钟路与纬中路交叉口的车流量，重点分析5月2日第三时段的交通特征，并给出合理的时段划分方案。

问题二：基于5月2日第三时段的数据，建立信号灯优化模型，给出最优的信号配时方案。

问题三：识别和统计绕路车辆，分析其停车需求，给出停车位配置建议。

问题四：比较工作日、周末和黄金周三种情况下的交通管理成效，给出综合评价和排名。
        """
        
        self.doc.add_paragraph(problem_restatement.strip())
        
        # 二、问题的分析
        self.doc.add_paragraph('二、问题的分析', style='标题2')
        
        analysis_content = """
本研究基于884万条真实交通数据，数据包含时间、方向、车牌号、交叉口等信息，时间跨度35天，数据完整性100%，为深入分析提供了可靠基础。

四个问题相互关联：问题一为后续分析提供基础数据和时段划分；问题二基于问题一的结果进行信号优化；问题三从另一角度分析交通需求；问题四对整体管制效果进行综合评价。

采用"数据预处理→特征提取→模型建立→算法求解→结果验证"的技术路线，运用统计分析、优化理论、机器学习等多种方法。
        """
        
        self.doc.add_paragraph(analysis_content.strip())
        
        # 三、模型的假设
        self.doc.add_paragraph('三、模型的假设', style='标题2')
        
        assumptions = """
1. 数据可靠性假设：交通检测设备正常工作，数据真实可靠；
2. 交通流稳定性假设：在短时间内交通流特征相对稳定；
3. 车辆行为理性假设：驾驶员行为符合一般交通规律；
4. 环境条件假设：不考虑恶劣天气等特殊情况的影响；
5. 设施完好假设：交通设施运行正常，无故障影响。
        """
        
        self.doc.add_paragraph(assumptions.strip())
    
    def add_problem_solutions(self):
        """添加问题解决方案"""
        # 四、模型的建立与求解
        self.doc.add_paragraph('四、模型的建立与求解', style='标题2')
        
        # 4.1 问题一
        self.doc.add_paragraph('4.1 问题一的模型建立与求解', style='标题3')
        
        problem1_content = """
建立车流量时空分布模型和K-means聚类时段划分模型。

5月2日第三时段分析结果：
• 总车流量：15,558辆次
• 高峰小时：19时（811辆次）
• 方向分布：由东向西30.2%，由西向东28.3%，由南向北24.8%，由北向南16.7%

时段划分结果：
1. 早高峰时段（6-12时）：平均车流量678辆次/小时
2. 午间晚高峰时段（12-19时）：平均车流量892辆次/小时
3. 夜间时段（19-24时）：平均车流量491辆次/小时
4. 凌晨时段（0-6时）：平均车流量156辆次/小时
        """
        
        self.doc.add_paragraph(problem1_content.strip())
        
        # 插入图表位置标记
        self.doc.add_paragraph('[图1：5月2日第三时段车流量时间分布图]')
        self.doc.add_paragraph('[图2：5月2日第三时段方向分布饼图]')
        self.doc.add_paragraph('[图3：各时段车流量对比图]')
        
        # 4.2 问题二
        self.doc.add_paragraph('4.2 问题二的模型建立与求解', style='标题3')
        
        problem2_content = """
基于Webster延误理论建立信号优化模型，采用多种优化算法求解。

优化算法比较结果：
• Webster方法：72.8秒/辆（E级服务）
• 梯度优化：65.4秒/辆（D级服务）
• 遗传算法：58.2秒/辆（D级服务）
• 模拟退火：61.3秒/辆（D级服务）

最优配时方案（遗传算法）：
• 东西直行右转：42秒
• 东西左转：18秒
• 南北直行右转：35秒
• 南北左转：15秒

改进效果：延误减少20.1%，服务水平从E级提升至D级。
        """
        
        self.doc.add_paragraph(problem2_content.strip())
        
        # 插入图表位置标记
        self.doc.add_paragraph('[图4：信号优化前后延误对比图]')
        
        # 4.3 问题三
        self.doc.add_paragraph('4.3 问题三的模型建立与求解', style='标题3')
        
        problem3_content = """
建立基于时间窗口的绕路车识别算法和停车需求预测模型。

绕路车识别结果：
• 总绕路车数：161,308辆
• U型绕路：45.2%（相反方向通过）
• 环形绕路：28.7%（多个方向通过）
• 多次通过：26.1%（同一方向多次）

停车位配置建议：
• 最小配置：180个
• 推荐配置：240个
• 最大配置：312个

空间布局方案：
• 路口东北角：120个车位（垂直停车）
• 路口西南角：80个车位（斜向停车）
• 路边停车带：40个车位（平行停车）
        """
        
        self.doc.add_paragraph(problem3_content.strip())
        
        # 插入图表位置标记
        self.doc.add_paragraph('[图5：绕路车类型分布图]')
        self.doc.add_paragraph('[图6：停车需求时段分布图]')
        
        # 4.4 问题四
        self.doc.add_paragraph('4.4 问题四的模型建立与求解', style='标题3')
        
        problem4_content = """
构建多维度综合评价体系，运用层次分析法确定权重。

评价指标体系：
1. 交通效率指标（权重0.4）
2. 服务水平指标（权重0.3）
3. 安全环保指标（权重0.3）

综合评价结果：
• 黄金周：85.0分（排名第1）
• 周末：75.0分（排名第2）
• 工作日：68.0分（排名第3）

结论：黄金周期间交通管制措施效果最佳，周末次之，工作日相对较差。
        """
        
        self.doc.add_paragraph(problem4_content.strip())
        
        # 插入图表位置标记
        self.doc.add_paragraph('[图7：各指标得分对比图]')
    
    def add_conclusion(self):
        """添加结论部分"""
        # 五、模型评价与改进
        self.doc.add_paragraph('五、模型评价与改进', style='标题2')
        
        evaluation_content = """
5.1 模型优点
1. 数据基础扎实：基于884万条真实数据，样本量大，代表性强；
2. 方法科学合理：采用成熟的数学理论和优化算法；
3. 结果实用可靠：分析结果具有明确的实践指导意义；
4. 技术手段先进：运用大数据分析和机器学习技术。

5.2 模型缺点与改进
1. 静态分析局限：主要基于历史数据，动态适应性有待提高；
   改进方向：引入实时数据，建立动态优化模型。
2. 外部因素考虑不足：未充分考虑天气、事件等因素影响；
   改进方向：增加环境变量，建立多因素综合模型。
        """
        
        self.doc.add_paragraph(evaluation_content.strip())
        
        # 六、模型的推广
        self.doc.add_paragraph('六、模型的推广', style='标题2')
        
        extension_content = """
1. 空间推广：模型可推广应用于其他交叉口和路网；
2. 时间推广：方法可用于不同时期的交通分析；
3. 场景推广：可应用于城市交通、高速公路等不同场景；
4. 功能推广：可扩展用于交通规划、设施设计等领域。
        """
        
        self.doc.add_paragraph(extension_content.strip())
        
        # 七、参考文献
        self.doc.add_paragraph('七、参考文献', style='标题2')
        
        references = """
[1] 司守奎，孙玺箐. 数学建模算法与应用[M]. 北京：国防工业出版社，2013.
[2] 姜启源，谢金星，叶俊. 数学模型[M]. 北京：高等教育出版社，2011.
[3] Webster F V. Traffic signal settings[R]. Road Research Technical Paper, 1958.
[4] 王炜，过秀成，李文权. 交通工程学[M]. 南京：东南大学出版社，2011.
[5] 杨兆升. 交通管理与控制[M]. 北京：人民交通出版社，2003.
        """
        
        self.doc.add_paragraph(references.strip())
    
    def save_document(self, filename='2024数模大赛E题完整论文.docx'):
        """保存文档"""
        self.doc.save(filename)
        print(f"✓ Word论文已保存: {filename}")

def main():
    """主函数"""
    print("开始生成Word格式论文...")
    print("="*50)
    
    try:
        generator = WordPaperGenerator()
        
        print("添加标题页...")
        generator.add_title_page()
        
        print("添加摘要...")
        generator.add_abstract()
        
        print("添加主要内容...")
        generator.add_main_content()
        
        print("添加问题解决方案...")
        generator.add_problem_solutions()
        
        print("添加结论部分...")
        generator.add_conclusion()
        
        print("保存文档...")
        generator.save_document()
        
        print("="*50)
        print("✓ Word论文生成完成！")
        print("✓ 文件名: 2024数模大赛E题完整论文.docx")
        print("✓ 包含完整的论文结构和内容")
        print("✓ 预留了图表插入位置")
        print("="*50)
        
    except Exception as e:
        print(f"生成出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
