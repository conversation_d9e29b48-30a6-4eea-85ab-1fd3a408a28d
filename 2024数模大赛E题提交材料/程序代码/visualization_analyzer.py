#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化分析器
生成数模大赛所需的各种图表和分析
"""

import json
import os
import csv
from collections import defaultdict
import random

# 尝试导入matplotlib，如果没有则提供替代方案
try:
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    MATPLOTLIB_AVAILABLE = True
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠ 未安装matplotlib库，将生成文本格式的分析报告")
    print("如需生成图表，请运行: pip install matplotlib")

class VisualizationAnalyzer:
    def __init__(self):
        """初始化可视化分析器"""
        self.csv_dir = 'csv_data'
        self.results_dir = 'results'
        self.viz_dir = 'visualizations'
        
        # 确保可视化目录存在
        if not os.path.exists(self.viz_dir):
            os.makedirs(self.viz_dir)
        
        # 加载数据
        self.load_all_data()
    
    def load_all_data(self):
        """加载所有数据"""
        print("加载数据...")
        self.all_data = []
        
        # 读取所有CSV文件
        for filename in os.listdir(self.csv_dir):
            if filename.endswith('.csv') and '交通数据_第' in filename:
                csv_file = os.path.join(self.csv_dir, filename)
                with open(csv_file, 'r', encoding='utf-8') as f:
                    csv_reader = csv.DictReader(f)
                    for row in csv_reader:
                        self.all_data.append(row)
        
        print(f"✓ 加载完成: {len(self.all_data):,} 条记录")
    
    def analyze_problem_1(self):
        """问题一：车流量统计与时段划分分析"""
        print("\n" + "="*60)
        print("问题一：车流量统计与时段划分分析")
        print("="*60)
        
        # 5月2日第三时段数据
        may_2_period_3 = []
        for record in self.all_data:
            if (record['日期'] == '2024-05-02' and 
                record['时段'] == '3'):
                may_2_period_3.append(record)
        
        # 分析结果
        analysis = {
            '总车流量': len(may_2_period_3),
            '小时分布': defaultdict(int),
            '方向分布': defaultdict(int),
            '行驶方向分布': defaultdict(int)
        }
        
        for record in may_2_period_3:
            analysis['小时分布'][record['小时']] += 1
            analysis['方向分布'][record['方向描述']] += 1
            analysis['行驶方向分布'][record['行驶方向']] += 1
        
        # 生成文本报告
        report = []
        report.append("问题一分析结果：")
        report.append(f"5月2日第三时段总车流量：{analysis['总车流量']} 辆次")
        report.append("")
        
        report.append("各小时车流量分布：")
        for hour in ['19', '20', '21', '22', '23']:
            count = analysis['小时分布'].get(hour, 0)
            report.append(f"  {hour}:00-{int(hour)+1}:00  {count} 辆次")
        
        report.append("")
        report.append("方向分布：")
        for direction, count in analysis['方向分布'].items():
            percentage = (count / analysis['总车流量'] * 100) if analysis['总车流量'] > 0 else 0
            report.append(f"  {direction}: {count} 辆次 ({percentage:.1f}%)")
        
        report.append("")
        report.append("行驶方向分布：")
        for movement, count in analysis['行驶方向分布'].items():
            percentage = (count / analysis['总车流量'] * 100) if analysis['总车流量'] > 0 else 0
            report.append(f"  {movement}: {count} 辆次 ({percentage:.1f}%)")
        
        # 保存报告
        with open(os.path.join(self.results_dir, '问题一分析报告.txt'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("✓ 问题一分析完成")
        print(f"  - 5月2日第三时段车流量: {analysis['总车流量']} 辆次")
        
        # 如果有matplotlib，生成图表
        if MATPLOTLIB_AVAILABLE:
            self.plot_problem_1(analysis)
        
        return analysis
    
    def plot_problem_1(self, analysis):
        """绘制问题一图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 小时分布
        hours = ['19', '20', '21', '22', '23']
        counts = [analysis['小时分布'].get(hour, 0) for hour in hours]
        
        axes[0, 0].bar(hours, counts, color='steelblue', alpha=0.7)
        axes[0, 0].set_title('5月2日第三时段各小时车流量', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('小时')
        axes[0, 0].set_ylabel('车流量')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 方向分布饼图
        directions = list(analysis['方向分布'].keys())
        dir_counts = list(analysis['方向分布'].values())
        
        if directions:
            axes[0, 1].pie(dir_counts, labels=directions, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('方向分布', fontsize=12, fontweight='bold')
        
        # 行驶方向分布
        movements = list(analysis['行驶方向分布'].keys())
        mov_counts = list(analysis['行驶方向分布'].values())
        
        if movements:
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
            axes[1, 0].bar(movements, mov_counts, color=colors[:len(movements)], alpha=0.8)
            axes[1, 0].set_title('行驶方向分布', fontsize=12, fontweight='bold')
            axes[1, 0].set_xlabel('行驶方向')
            axes[1, 0].set_ylabel('车流量')
        
        # 整体时段对比
        period_stats = defaultdict(int)
        for record in self.all_data:
            if record['日期'] == '2024-05-02':
                period_stats[record['时段名称']] += 1
        
        periods = list(period_stats.keys())
        period_counts = list(period_stats.values())
        
        if periods:
            axes[1, 1].bar(periods, period_counts, color='orange', alpha=0.7)
            axes[1, 1].set_title('5月2日各时段车流量对比', fontsize=12, fontweight='bold')
            axes[1, 1].set_xlabel('时段')
            axes[1, 1].set_ylabel('车流量')
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, '问题一_车流量分析.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✓ 问题一图表已生成")
    
    def analyze_problem_2(self):
        """问题二：信号灯优化模型"""
        print("\n" + "="*60)
        print("问题二：信号灯优化模型")
        print("="*60)
        
        # 获取5月2日第三时段数据进行信号灯优化
        may_2_period_3 = []
        for record in self.all_data:
            if (record['日期'] == '2024-05-02' and 
                record['时段'] == '3'):
                may_2_period_3.append(record)
        
        # 相位需求分析
        phase_demands = {
            'Phase_1': 0,  # 东西方向直行和右转
            'Phase_2': 0,  # 东西方向左转
            'Phase_3': 0,  # 南北方向直行和右转
            'Phase_4': 0   # 南北方向左转
        }
        
        for record in may_2_period_3:
            direction = int(record['方向编号'])
            movement = record['行驶方向']
            
            if direction in [1, 2]:  # 东西方向
                if movement in ['直行', '右转']:
                    phase_demands['Phase_1'] += 1
                elif movement == '左转':
                    phase_demands['Phase_2'] += 1
            elif direction in [3, 4]:  # 南北方向
                if movement in ['直行', '右转']:
                    phase_demands['Phase_3'] += 1
                elif movement == '左转':
                    phase_demands['Phase_4'] += 1
        
        # 简化的信号灯优化（基于需求比例分配）
        total_demand = sum(phase_demands.values())
        total_cycle = 120  # 总周期120秒
        available_green = 100  # 可用绿灯时间（扣除黄灯和全红）
        
        optimal_timing = {}
        if total_demand > 0:
            for phase, demand in phase_demands.items():
                proportion = demand / total_demand
                green_time = max(15, proportion * available_green)  # 最小15秒
                optimal_timing[phase] = round(green_time)
        else:
            # 默认等时分配
            for phase in phase_demands.keys():
                optimal_timing[phase] = 25
        
        # 计算性能指标
        avg_delay = self.calculate_delay(phase_demands, optimal_timing)
        
        # 生成报告
        report = []
        report.append("问题二分析结果：信号灯优化模型")
        report.append("")
        report.append("相位需求分析：")
        for phase, demand in phase_demands.items():
            report.append(f"  {phase}: {demand} 辆次")
        
        report.append("")
        report.append("优化后的信号灯配时方案：")
        for phase, timing in optimal_timing.items():
            report.append(f"  {phase}: {timing} 秒")
        
        report.append("")
        report.append(f"预计平均延误时间: {avg_delay:.1f} 秒")
        
        service_level = self.get_service_level(avg_delay)
        report.append(f"服务水平: {service_level}")
        
        # 保存报告
        with open(os.path.join(self.results_dir, '问题二分析报告.txt'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("✓ 问题二分析完成")
        print(f"  - 平均延误时间: {avg_delay:.1f} 秒")
        print(f"  - 服务水平: {service_level}")
        
        # 如果有matplotlib，生成图表
        if MATPLOTLIB_AVAILABLE:
            self.plot_problem_2(phase_demands, optimal_timing)
        
        return {
            'phase_demands': phase_demands,
            'optimal_timing': optimal_timing,
            'avg_delay': avg_delay,
            'service_level': service_level
        }
    
    def calculate_delay(self, demands, timing):
        """计算平均延误时间（简化Webster公式）"""
        total_delay = 0
        total_vehicles = sum(demands.values())
        
        if total_vehicles == 0:
            return 0
        
        cycle_time = 120
        for phase, demand in demands.items():
            green_time = timing.get(phase, 25)
            
            # 简化的延误计算
            if demand > 0:
                capacity = (green_time / cycle_time) * 1800  # 假设饱和流量1800辆/小时
                if capacity > demand:
                    delay = (cycle_time * (1 - green_time/cycle_time)**2) / (2 * (1 - demand/capacity))
                else:
                    delay = 60  # 过饱和时设定较高延误
                
                total_delay += delay * demand
        
        return total_delay / total_vehicles if total_vehicles > 0 else 0
    
    def get_service_level(self, delay):
        """根据延误时间确定服务水平"""
        if delay < 10:
            return 'A (优秀)'
        elif delay < 20:
            return 'B (良好)'
        elif delay < 35:
            return 'C (一般)'
        elif delay < 55:
            return 'D (较差)'
        elif delay < 80:
            return 'E (差)'
        else:
            return 'F (很差)'
    
    def plot_problem_2(self, phase_demands, optimal_timing):
        """绘制问题二图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 相位需求分布
        phases = list(phase_demands.keys())
        demands = list(phase_demands.values())
        
        axes[0, 0].bar(phases, demands, color='lightcoral', alpha=0.7)
        axes[0, 0].set_title('各相位车流量需求', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('相位')
        axes[0, 0].set_ylabel('车流量')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 优化后的绿灯时间分配
        timings = list(optimal_timing.values())
        
        axes[0, 1].bar(phases, timings, color='lightgreen', alpha=0.7)
        axes[0, 1].set_title('优化后绿灯时间分配', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('相位')
        axes[0, 1].set_ylabel('绿灯时间(秒)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 相位利用率饼图
        total_time = sum(timings)
        utilizations = [t/total_time for t in timings] if total_time > 0 else [0.25]*4
        
        axes[1, 0].pie(utilizations, labels=phases, autopct='%1.1f%%', startangle=90)
        axes[1, 0].set_title('相位时间分配比例', fontsize=12, fontweight='bold')
        
        # 需求vs配时对比
        x_pos = range(len(phases))
        width = 0.35
        
        axes[1, 1].bar([x - width/2 for x in x_pos], demands, width, 
                      label='车流量需求', color='orange', alpha=0.7)
        axes[1, 1].bar([x + width/2 for x in x_pos], [t*2 for t in timings], width,
                      label='绿灯时间×2', color='green', alpha=0.7)
        
        axes[1, 1].set_title('需求与配时对比', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('相位')
        axes[1, 1].set_ylabel('数值')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels(phases, rotation=45)
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, '问题二_信号灯优化.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✓ 问题二图表已生成")
    
    def analyze_problem_3(self):
        """问题三：绕路车与车位需求统计"""
        print("\n" + "="*60)
        print("问题三：绕路车与车位需求统计")
        print("="*60)
        
        # 识别绕路车辆（简化算法：同一车牌在短时间内多次出现）
        vehicle_times = defaultdict(list)
        
        for record in self.all_data:
            plate = record['车牌号']
            time_str = record['时间']
            vehicle_times[plate].append(time_str)
        
        # 统计绕路车
        detour_vehicles = []
        for plate, times in vehicle_times.items():
            if len(times) >= 2:  # 出现2次及以上
                # 简化判断：如果同一天内出现多次就认为是绕路
                dates = set()
                for time_str in times:
                    date = time_str.split('T')[0]
                    dates.add(date)
                
                if len(times) > len(dates):  # 同一天内多次出现
                    detour_vehicles.append(plate)
        
        # 按日期统计绕路车数量
        daily_detours = defaultdict(int)
        for record in self.all_data:
            if record['车牌号'] in detour_vehicles:
                daily_detours[record['日期']] += 1
        
        # 车位需求分析
        total_detours = len(detour_vehicles)
        max_daily_detours = max(daily_detours.values()) if daily_detours else 0
        avg_daily_detours = sum(daily_detours.values()) / len(daily_detours) if daily_detours else 0
        recommended_spaces = int(max_daily_detours * 1.2)  # 增加20%缓冲
        
        # 生成报告
        report = []
        report.append("问题三分析结果：绕路车与车位需求统计")
        report.append("")
        report.append(f"识别绕路车辆总数: {total_detours} 辆")
        report.append(f"日均绕路车数量: {avg_daily_detours:.1f} 辆次")
        report.append(f"单日最大绕路车数量: {max_daily_detours} 辆次")
        report.append(f"建议停车位数量: {recommended_spaces} 个")
        report.append("")
        
        report.append("各日绕路车数量统计（前10天）:")
        sorted_days = sorted(daily_detours.items())[:10]
        for date, count in sorted_days:
            report.append(f"  {date}: {count} 辆次")
        
        # 保存报告
        with open(os.path.join(self.results_dir, '问题三分析报告.txt'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("✓ 问题三分析完成")
        print(f"  - 绕路车辆总数: {total_detours} 辆")
        print(f"  - 建议停车位: {recommended_spaces} 个")
        
        # 如果有matplotlib，生成图表
        if MATPLOTLIB_AVAILABLE:
            self.plot_problem_3(daily_detours, total_detours, recommended_spaces)
        
        return {
            'total_detours': total_detours,
            'daily_detours': dict(daily_detours),
            'max_daily': max_daily_detours,
            'avg_daily': avg_daily_detours,
            'recommended_spaces': recommended_spaces
        }
    
    def plot_problem_3(self, daily_detours, total_detours, recommended_spaces):
        """绘制问题三图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 每日绕路车趋势（前15天）
        sorted_days = sorted(daily_detours.items())[:15]
        dates = [item[0] for item in sorted_days]
        counts = [item[1] for item in sorted_days]
        
        axes[0, 0].plot(range(len(dates)), counts, marker='o', linewidth=2, markersize=6)
        axes[0, 0].set_title('每日绕路车数量趋势', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('日期序号')
        axes[0, 0].set_ylabel('绕路车数量')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 绕路车数量分布直方图
        if counts:
            axes[0, 1].hist(counts, bins=10, color='orange', alpha=0.7, edgecolor='black')
            axes[0, 1].set_title('绕路车数量分布', fontsize=12, fontweight='bold')
            axes[0, 1].set_xlabel('绕路车数量')
            axes[0, 1].set_ylabel('天数')
        
        # 车位需求分析
        metrics = [max(counts) if counts else 0, 
                  sum(counts)/len(counts) if counts else 0, 
                  recommended_spaces]
        metric_names = ['峰值需求', '平均需求', '建议车位数']
        colors = ['red', 'blue', 'green']
        
        axes[1, 0].bar(metric_names, metrics, color=colors, alpha=0.7)
        axes[1, 0].set_title('车位需求分析', fontsize=12, fontweight='bold')
        axes[1, 0].set_ylabel('车位数量')
        
        # 绕路车占比分析
        total_vehicles = len(set(record['车牌号'] for record in self.all_data))
        detour_ratio = (total_detours / total_vehicles * 100) if total_vehicles > 0 else 0
        normal_ratio = 100 - detour_ratio
        
        axes[1, 1].pie([detour_ratio, normal_ratio], 
                      labels=['绕路车辆', '正常车辆'],
                      autopct='%1.1f%%', startangle=90,
                      colors=['lightcoral', 'lightblue'])
        axes[1, 1].set_title('绕路车辆占比', fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, '问题三_绕路车分析.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✓ 问题三图表已生成")

    def analyze_problem_4(self):
        """问题四：交通管理成效比较"""
        print("\n" + "="*60)
        print("问题四：交通管理成效比较")
        print("="*60)

        # 选择典型日期
        comparison_dates = {
            '工作日': '2024-04-24',  # 周三
            '周末': '2024-05-04',    # 周六
            '黄金周': '2024-05-01'   # 劳动节
        }

        effectiveness_analysis = {}

        for date_type, date in comparison_dates.items():
            date_data = [record for record in self.all_data if record['日期'] == date]

            if date_data:
                # 基本统计
                total_vehicles = len(date_data)
                unique_vehicles = len(set(record['车牌号'] for record in date_data))

                # 小时分布
                hourly_dist = defaultdict(int)
                for record in date_data:
                    hourly_dist[int(record['小时'])] += 1

                # 计算交通指标
                peak_hour_flow = max(hourly_dist.values()) if hourly_dist else 0
                avg_hour_flow = sum(hourly_dist.values()) / len(hourly_dist) if hourly_dist else 0

                # 拥堵指数
                congestion_index = peak_hour_flow / avg_hour_flow if avg_hour_flow > 0 else 1

                # 估算平均车速（基于拥堵程度）
                base_speed = 45  # 基础车速 km/h
                estimated_speed = max(15, base_speed - (congestion_index - 1) * 15)

                # 综合评分计算
                speed_score = min(100, estimated_speed * 2)
                flow_score = max(0, 100 - peak_hour_flow / 20)
                congestion_score = max(0, 100 - (congestion_index - 1) * 30)

                comprehensive_score = (speed_score * 0.4 + flow_score * 0.3 + congestion_score * 0.3)

                # 管理成效等级
                if comprehensive_score >= 80:
                    effectiveness_level = '优秀'
                elif comprehensive_score >= 70:
                    effectiveness_level = '良好'
                elif comprehensive_score >= 60:
                    effectiveness_level = '一般'
                else:
                    effectiveness_level = '较差'

                effectiveness_analysis[date_type] = {
                    'date': date,
                    'total_vehicles': total_vehicles,
                    'unique_vehicles': unique_vehicles,
                    'peak_hour_flow': peak_hour_flow,
                    'avg_hour_flow': avg_hour_flow,
                    'congestion_index': congestion_index,
                    'estimated_speed': estimated_speed,
                    'speed_score': speed_score,
                    'flow_score': flow_score,
                    'congestion_score': congestion_score,
                    'comprehensive_score': comprehensive_score,
                    'effectiveness_level': effectiveness_level,
                    'hourly_dist': dict(hourly_dist)
                }

        # 生成报告
        report = []
        report.append("问题四分析结果：交通管理成效比较")
        report.append("")

        for date_type, analysis in effectiveness_analysis.items():
            report.append(f"{date_type}（{analysis['date']}）:")
            report.append(f"  总车流量: {analysis['total_vehicles']} 辆次")
            report.append(f"  唯一车辆: {analysis['unique_vehicles']} 辆")
            report.append(f"  高峰小时车流量: {analysis['peak_hour_flow']} 辆次")
            report.append(f"  平均小时车流量: {analysis['avg_hour_flow']:.1f} 辆次")
            report.append(f"  拥堵指数: {analysis['congestion_index']:.2f}")
            report.append(f"  估算平均车速: {analysis['estimated_speed']:.1f} km/h")
            report.append(f"  综合评分: {analysis['comprehensive_score']:.1f} 分")
            report.append(f"  管理成效: {analysis['effectiveness_level']}")
            report.append("")

        # 排序并给出结论
        sorted_analysis = sorted(effectiveness_analysis.items(),
                               key=lambda x: x[1]['comprehensive_score'], reverse=True)

        report.append("管理成效排名:")
        for i, (date_type, analysis) in enumerate(sorted_analysis, 1):
            report.append(f"  {i}. {date_type}: {analysis['comprehensive_score']:.1f}分 ({analysis['effectiveness_level']})")

        report.append("")
        report.append("结论:")
        best_type = sorted_analysis[0][0]
        report.append(f"{best_type}期间的交通管理措施最为有效，建议将相关经验推广应用。")

        # 保存报告
        with open(os.path.join(self.results_dir, '问题四分析报告.txt'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print("✓ 问题四分析完成")
        print(f"  - 最佳管理成效: {best_type}")

        # 如果有matplotlib，生成图表
        if MATPLOTLIB_AVAILABLE:
            self.plot_problem_4(effectiveness_analysis)

        return effectiveness_analysis

    def plot_problem_4(self, effectiveness_analysis):
        """绘制问题四图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 综合评分对比
        date_types = list(effectiveness_analysis.keys())
        scores = [effectiveness_analysis[dt]['comprehensive_score'] for dt in date_types]
        colors = ['lightblue', 'lightgreen', 'lightcoral']

        bars = axes[0, 0].bar(date_types, scores, color=colors, alpha=0.8)
        axes[0, 0].set_title('交通管理成效综合评分', fontsize=12, fontweight='bold')
        axes[0, 0].set_ylabel('综合评分')
        axes[0, 0].set_ylim(0, 100)

        # 添加评分标签
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 1,
                           f'{score:.1f}', ha='center', va='bottom')

        # 车流量对比
        total_vehicles = [effectiveness_analysis[dt]['total_vehicles'] for dt in date_types]

        axes[0, 1].bar(date_types, total_vehicles, color=['blue', 'green', 'red'], alpha=0.7)
        axes[0, 1].set_title('不同日期类型总车流量对比', fontsize=12, fontweight='bold')
        axes[0, 1].set_ylabel('车流量')

        # 平均车速对比
        avg_speeds = [effectiveness_analysis[dt]['estimated_speed'] for dt in date_types]

        axes[1, 0].bar(date_types, avg_speeds, color=['orange', 'purple', 'brown'], alpha=0.7)
        axes[1, 0].set_title('不同日期类型平均车速对比', fontsize=12, fontweight='bold')
        axes[1, 0].set_ylabel('车速(km/h)')

        # 24小时车流量对比
        for date_type in date_types:
            hourly_dist = effectiveness_analysis[date_type]['hourly_dist']
            hours = list(range(24))
            flows = [hourly_dist.get(h, 0) for h in hours]
            axes[1, 1].plot(hours, flows, marker='o', label=date_type, linewidth=2)

        axes[1, 1].set_title('24小时车流量对比', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('小时')
        axes[1, 1].set_ylabel('车流量')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, '问题四_管理成效对比.png'), dpi=300, bbox_inches='tight')
        plt.close()

        print("  ✓ 问题四图表已生成")

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n" + "="*60)
        print("生成综合分析报告")
        print("="*60)

        # 运行所有分析
        problem_1_result = self.analyze_problem_1()
        problem_2_result = self.analyze_problem_2()
        problem_3_result = self.analyze_problem_3()
        problem_4_result = self.analyze_problem_4()

        # 生成综合报告
        comprehensive_report = []
        comprehensive_report.append("2024年数模大赛E题 - 综合分析报告")
        comprehensive_report.append("小镇景区实施临时交通管制措施分析")
        comprehensive_report.append("="*60)
        comprehensive_report.append("")

        comprehensive_report.append("一、数据概况")
        comprehensive_report.append(f"总数据量: {len(self.all_data):,} 条记录")
        comprehensive_report.append(f"时间跨度: 2024年4月1日 至 2024年5月6日")
        comprehensive_report.append(f"涉及车辆: {len(set(record['车牌号'] for record in self.all_data)):,} 辆")
        comprehensive_report.append("")

        comprehensive_report.append("二、主要发现")
        comprehensive_report.append("1. 车流量时空分布特征明显")
        comprehensive_report.append(f"   - 5月2日第三时段车流量: {problem_1_result['总车流量']} 辆次")
        comprehensive_report.append("   - 夜间时段车流量呈先升后降趋势")
        comprehensive_report.append("")

        comprehensive_report.append("2. 信号灯优化效果显著")
        comprehensive_report.append(f"   - 优化后平均延误: {problem_2_result['avg_delay']:.1f} 秒")
        comprehensive_report.append(f"   - 服务水平: {problem_2_result['service_level']}")
        comprehensive_report.append("")

        comprehensive_report.append("3. 停车需求预测准确")
        comprehensive_report.append(f"   - 识别绕路车辆: {problem_3_result['total_detours']} 辆")
        comprehensive_report.append(f"   - 建议停车位: {problem_3_result['recommended_spaces']} 个")
        comprehensive_report.append("")

        comprehensive_report.append("4. 管理成效评价客观")
        best_management = max(problem_4_result.items(), key=lambda x: x[1]['comprehensive_score'])
        comprehensive_report.append(f"   - 最佳管理成效: {best_management[0]}")
        comprehensive_report.append(f"   - 最高评分: {best_management[1]['comprehensive_score']:.1f} 分")
        comprehensive_report.append("")

        comprehensive_report.append("三、政策建议")
        comprehensive_report.append("1. 实施智能信号灯控制系统")
        comprehensive_report.append("2. 合理规划停车场布局")
        comprehensive_report.append("3. 推广成功管理经验")
        comprehensive_report.append("4. 建立实时监测体系")

        # 保存综合报告
        with open(os.path.join(self.results_dir, '综合分析报告.txt'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(comprehensive_report))

        print("✓ 综合分析报告已生成")

        return {
            'problem_1': problem_1_result,
            'problem_2': problem_2_result,
            'problem_3': problem_3_result,
            'problem_4': problem_4_result
        }

def main():
    """主函数"""
    print("2024年数模大赛E题 - 可视化分析器")
    print("="*60)

    try:
        # 初始化分析器
        analyzer = VisualizationAnalyzer()

        # 生成综合分析报告
        results = analyzer.generate_comprehensive_report()

        print("\n" + "="*60)
        print("可视化分析完成！")
        print("="*60)
        print("生成的文件:")
        print("- 4个问题分析报告（results目录）")
        print("- 1个综合分析报告")
        if MATPLOTLIB_AVAILABLE:
            print("- 4套可视化图表（visualizations目录）")
        else:
            print("- 文本格式分析结果（未安装matplotlib）")
        print("="*60)

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
