#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CSV到Excel转换器
将生成的CSV文件转换为Excel格式，包含多个工作表
"""

import csv
import json
import os
from datetime import datetime

# 尝试导入openpyxl，如果没有则提供安装提示
try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("⚠ 未安装openpyxl库，将跳过Excel文件生成")
    print("如需生成Excel文件，请运行: pip install openpyxl")

class ExcelConverter:
    def __init__(self):
        """初始化Excel转换器"""
        self.csv_dir = 'csv_data'
        self.excel_dir = 'excel_data'
        self.results_dir = 'results'
        
        # 确保Excel目录存在
        if not os.path.exists(self.excel_dir):
            os.makedirs(self.excel_dir)
    
    def convert_csv_to_excel(self, csv_file, stats_file, excel_file):
        """将单个CSV文件转换为Excel文件"""
        if not OPENPYXL_AVAILABLE:
            print(f"跳过 {excel_file} 的生成（缺少openpyxl库）")
            return False
        
        try:
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 1. 创建原始数据工作表
            ws_data = wb.create_sheet("原始数据")
            
            # 读取CSV数据
            with open(csv_file, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                for row_num, row in enumerate(csv_reader, 1):
                    for col_num, value in enumerate(row, 1):
                        ws_data.cell(row=row_num, column=col_num, value=value)
            
            # 设置标题行样式
            for cell in ws_data[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # 2. 创建数据摘要工作表
            if os.path.exists(stats_file):
                ws_summary = wb.create_sheet("数据摘要")
                
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                
                # 写入摘要信息
                row = 1
                ws_summary.cell(row=row, column=1, value="项目").font = Font(bold=True)
                ws_summary.cell(row=row, column=2, value="数值").font = Font(bold=True)
                row += 1
                
                # 基本信息
                basic_info = [
                    ("文件编号", stats.get('文件编号', 'N/A')),
                    ("数据行数", stats.get('数据行数', 0)),
                    ("开始时间", stats.get('开始时间', 'N/A')),
                    ("结束时间", stats.get('结束时间', 'N/A'))
                ]
                
                for item, value in basic_info:
                    ws_summary.cell(row=row, column=1, value=item)
                    ws_summary.cell(row=row, column=2, value=str(value))
                    row += 1
                
                # 3. 创建方向分布工作表
                ws_direction = wb.create_sheet("方向分布")
                ws_direction.cell(row=1, column=1, value="方向").font = Font(bold=True)
                ws_direction.cell(row=1, column=2, value="车流量").font = Font(bold=True)
                
                direction_dist = stats.get('方向分布', {})
                for i, (direction, count) in enumerate(direction_dist.items(), 2):
                    ws_direction.cell(row=i, column=1, value=direction)
                    ws_direction.cell(row=i, column=2, value=count)
                
                # 4. 创建时段分布工作表
                ws_period = wb.create_sheet("时段分布")
                ws_period.cell(row=1, column=1, value="时段").font = Font(bold=True)
                ws_period.cell(row=1, column=2, value="车流量").font = Font(bold=True)
                
                period_dist = stats.get('时段分布', {})
                for i, (period, count) in enumerate(period_dist.items(), 2):
                    ws_period.cell(row=i, column=1, value=period)
                    ws_period.cell(row=i, column=2, value=count)
                
                # 5. 创建行驶方向分布工作表
                ws_movement = wb.create_sheet("行驶方向分布")
                ws_movement.cell(row=1, column=1, value="行驶方向").font = Font(bold=True)
                ws_movement.cell(row=1, column=2, value="车流量").font = Font(bold=True)
                
                movement_dist = stats.get('行驶方向分布', {})
                for i, (movement, count) in enumerate(movement_dist.items(), 2):
                    ws_movement.cell(row=i, column=1, value=movement)
                    ws_movement.cell(row=i, column=2, value=count)
            
            # 保存Excel文件
            wb.save(excel_file)
            return True
            
        except Exception as e:
            print(f"转换 {csv_file} 失败: {e}")
            return False
    
    def convert_all_files(self):
        """转换所有CSV文件为Excel格式"""
        print("="*60)
        print("开始转换CSV文件为Excel格式")
        print("="*60)
        
        if not OPENPYXL_AVAILABLE:
            print("无法转换为Excel格式，请安装openpyxl库")
            return False
        
        success_count = 0
        total_files = 0
        
        # 遍历CSV文件
        for filename in os.listdir(self.csv_dir):
            if filename.endswith('.csv') and '交通数据_第' in filename:
                total_files += 1
                
                # 构建文件路径
                csv_file = os.path.join(self.csv_dir, filename)
                
                # 对应的统计文件
                stats_filename = filename.replace('交通数据_第', '统计信息_第').replace('.csv', '.json')
                stats_file = os.path.join(self.results_dir, stats_filename)
                
                # Excel文件路径
                excel_filename = filename.replace('.csv', '.xlsx')
                excel_file = os.path.join(self.excel_dir, excel_filename)
                
                print(f"转换: {filename} -> {excel_filename}")
                
                if self.convert_csv_to_excel(csv_file, stats_file, excel_file):
                    success_count += 1
                    print(f"✓ 成功转换: {excel_filename}")
                else:
                    print(f"✗ 转换失败: {excel_filename}")
        
        print(f"\n转换完成: {success_count}/{total_files} 个文件成功转换")
        return success_count == total_files
    
    def create_summary_excel(self):
        """创建总体摘要Excel文件"""
        if not OPENPYXL_AVAILABLE:
            return False
        
        print("\n创建总体摘要Excel文件...")
        
        try:
            # 读取总体摘要
            summary_file = os.path.join(self.results_dir, 'overall_summary.json')
            if not os.path.exists(summary_file):
                print("总体摘要文件不存在")
                return False
            
            with open(summary_file, 'r', encoding='utf-8') as f:
                summary = json.load(f)
            
            # 创建工作簿
            wb = Workbook()
            wb.remove(wb.active)
            
            # 1. 数据概况工作表
            ws_overview = wb.create_sheet("数据概况")
            overview_data = summary.get('数据概况', {})
            
            ws_overview.cell(row=1, column=1, value="项目").font = Font(bold=True)
            ws_overview.cell(row=1, column=2, value="数值").font = Font(bold=True)
            
            for i, (key, value) in enumerate(overview_data.items(), 2):
                ws_overview.cell(row=i, column=1, value=key)
                ws_overview.cell(row=i, column=2, value=str(value))
            
            # 2. 方向分布工作表
            ws_direction = wb.create_sheet("方向分布统计")
            direction_data = summary.get('方向分布', {})
            
            ws_direction.cell(row=1, column=1, value="方向").font = Font(bold=True)
            ws_direction.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            ws_direction.cell(row=1, column=3, value="占比(%)").font = Font(bold=True)
            
            total_direction = sum(direction_data.values())
            for i, (direction, count) in enumerate(direction_data.items(), 2):
                ws_direction.cell(row=i, column=1, value=direction)
                ws_direction.cell(row=i, column=2, value=count)
                percentage = (count / total_direction * 100) if total_direction > 0 else 0
                ws_direction.cell(row=i, column=3, value=f"{percentage:.1f}%")
            
            # 3. 时段分布工作表
            ws_period = wb.create_sheet("时段分布统计")
            period_data = summary.get('时段分布', {})
            
            ws_period.cell(row=1, column=1, value="时段").font = Font(bold=True)
            ws_period.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            ws_period.cell(row=1, column=3, value="占比(%)").font = Font(bold=True)
            
            total_period = sum(period_data.values())
            for i, (period, count) in enumerate(period_data.items(), 2):
                ws_period.cell(row=i, column=1, value=period)
                ws_period.cell(row=i, column=2, value=count)
                percentage = (count / total_period * 100) if total_period > 0 else 0
                ws_period.cell(row=i, column=3, value=f"{percentage:.1f}%")
            
            # 4. 小时分布工作表
            ws_hour = wb.create_sheet("24小时分布")
            hour_data = summary.get('小时分布', {})
            
            ws_hour.cell(row=1, column=1, value="小时").font = Font(bold=True)
            ws_hour.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            
            # 按小时排序
            sorted_hours = sorted(hour_data.items(), key=lambda x: int(x[0]))
            for i, (hour, count) in enumerate(sorted_hours, 2):
                ws_hour.cell(row=i, column=1, value=f"{hour}:00")
                ws_hour.cell(row=i, column=2, value=count)
            
            # 5. 5月2日第三时段分析
            may_2_file = os.path.join(self.results_dir, 'may_2_period_3_analysis.json')
            if os.path.exists(may_2_file):
                with open(may_2_file, 'r', encoding='utf-8') as f:
                    may_2_data = json.load(f)
                
                ws_may2 = wb.create_sheet("5月2日第三时段")
                
                # 基本信息
                ws_may2.cell(row=1, column=1, value="分析项目").font = Font(bold=True)
                ws_may2.cell(row=1, column=2, value="数值").font = Font(bold=True)
                
                basic_may2 = [
                    ("总车流量", may_2_data.get('总车流量', 0)),
                    ("唯一车辆数", may_2_data.get('唯一车辆数', 0)),
                    ("高峰小时", f"{may_2_data.get('高峰小时', 'N/A')}时"),
                    ("高峰小时车流量", may_2_data.get('高峰小时车流量', 0))
                ]
                
                for i, (item, value) in enumerate(basic_may2, 2):
                    ws_may2.cell(row=i, column=1, value=item)
                    ws_may2.cell(row=i, column=2, value=str(value))
                
                # 小时分布
                row = len(basic_may2) + 3
                ws_may2.cell(row=row, column=1, value="小时").font = Font(bold=True)
                ws_may2.cell(row=row, column=2, value="车流量").font = Font(bold=True)
                
                hour_dist = may_2_data.get('小时分布', {})
                for hour in ['19', '20', '21', '22', '23']:
                    row += 1
                    ws_may2.cell(row=row, column=1, value=f"{hour}:00")
                    ws_may2.cell(row=row, column=2, value=hour_dist.get(hour, 0))
            
            # 保存文件
            excel_file = os.path.join(self.excel_dir, '数据分析总体摘要.xlsx')
            wb.save(excel_file)
            print(f"✓ 总体摘要Excel文件已生成: {excel_file}")
            return True
            
        except Exception as e:
            print(f"创建总体摘要Excel失败: {e}")
            return False

def main():
    """主函数"""
    print("2024年数模大赛E题 - Excel转换器")
    print("="*60)
    
    try:
        converter = ExcelConverter()
        
        # 转换所有CSV文件
        success = converter.convert_all_files()
        
        # 创建总体摘要Excel
        converter.create_summary_excel()
        
        if success:
            print("\n" + "="*60)
            print("Excel转换完成！")
            print("="*60)
            print("生成的Excel文件:")
            print("- 10个分割数据Excel文件（excel_data目录）")
            print("- 1个总体摘要Excel文件")
            print("- 每个Excel文件包含多个工作表：")
            print("  * 原始数据")
            print("  * 数据摘要") 
            print("  * 方向分布")
            print("  * 时段分布")
            print("  * 行驶方向分布")
            print("="*60)
        else:
            print("部分文件转换失败，请检查错误信息")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
