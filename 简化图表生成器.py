#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版论文图表生成器
"""

import matplotlib.pyplot as plt
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def create_output_dir():
    """创建输出目录"""
    output_dir = "论文图表"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    return output_dir

def generate_chart_1():
    """图1：5月2日第三时段车流量时间分布图"""
    print("生成图1...")
    
    hours = [19, 20, 21, 22, 23]
    flows = [811, 698, 542, 398, 365]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    bars = ax.bar(hours, flows, color='steelblue', alpha=0.8)
    
    # 标注数值
    for bar, flow in zip(bars, flows):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 10,
               f'{flow}', ha='center', va='bottom', fontweight='bold')
    
    # 突出高峰
    bars[0].set_color('red')
    
    ax.set_xlabel('时间（小时）')
    ax.set_ylabel('车流量（辆次）')
    ax.set_title('图1：5月2日第三时段车流量时间分布图', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图1_车流量时间分布.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图1完成")

def generate_chart_2():
    """图2：方向分布饼图"""
    print("生成图2...")
    
    directions = ['由东向西', '由西向东', '由南向北', '由北向南']
    percentages = [30.2, 28.3, 24.8, 16.7]
    
    fig, ax = plt.subplots(figsize=(8, 8))
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    wedges, texts, autotexts = ax.pie(percentages, labels=directions, autopct='%1.1f%%',
                                     colors=colors, startangle=90)
    
    ax.set_title('图2：5月2日第三时段方向分布饼图', fontsize=14, fontweight='bold')
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图2_方向分布饼图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图2完成")

def generate_chart_3():
    """图3：时段划分对比"""
    print("生成图3...")
    
    periods = ['凌晨\n(0-6时)', '早高峰\n(6-12时)', '午间晚高峰\n(12-19时)', '夜间\n(19-24时)']
    flows = [156, 678, 892, 491]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    bars = ax.bar(periods, flows, color=['lightblue', 'orange', 'red', 'green'], alpha=0.8)
    
    # 标注数值
    for bar, flow in zip(bars, flows):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 15,
               f'{flow}', ha='center', va='bottom', fontweight='bold')
    
    ax.set_ylabel('平均车流量（辆次/小时）')
    ax.set_title('图3：各时段车流量对比图', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='y')
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图3_时段对比图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图3完成")

def generate_chart_4():
    """图4：信号优化对比"""
    print("生成图4...")
    
    methods = ['Webster\n方法', '梯度\n优化', '遗传\n算法', '模拟\n退火']
    delays = [72.8, 65.4, 58.2, 61.3]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    colors = ['red' if d > 70 else 'orange' if d > 60 else 'green' for d in delays]
    bars = ax.bar(methods, delays, color=colors, alpha=0.8)
    
    # 标注数值
    for bar, delay in zip(bars, delays):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
               f'{delay}s', ha='center', va='bottom', fontweight='bold')
    
    ax.set_ylabel('平均延误时间（秒/辆）')
    ax.set_title('图4：信号优化前后延误对比图', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='y')
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图4_信号优化对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图4完成")

def generate_chart_5():
    """图5：绕路车类型分布"""
    print("生成图5...")
    
    types = ['U型绕路', '环形绕路', '多次通过']
    percentages = [45.2, 28.7, 26.1]
    
    fig, ax = plt.subplots(figsize=(8, 8))
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    wedges, texts, autotexts = ax.pie(percentages, labels=types, autopct='%1.1f%%',
                                     colors=colors, startangle=90)
    
    ax.set_title('图5：绕路车类型分布图', fontsize=14, fontweight='bold')
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图5_绕路车类型分布.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图5完成")

def generate_chart_6():
    """图6：停车需求分布"""
    print("生成图6...")
    
    hours = list(range(0, 24, 2))
    demands = [12, 8, 15, 28, 35, 45, 48, 52, 58, 55, 42, 35]
    
    fig, ax = plt.subplots(figsize=(12, 6))
    ax.plot(hours, demands, 'b-o', linewidth=2, markersize=6)
    ax.fill_between(hours, demands, alpha=0.3)
    
    # 标注最高峰
    max_idx = demands.index(max(demands))
    ax.annotate(f'最高峰\n{max(demands)}个', xy=(hours[max_idx], max(demands)),
               xytext=(hours[max_idx]+2, max(demands)+8),
               arrowprops=dict(arrowstyle='->', color='red'))
    
    ax.set_xlabel('时间（小时）')
    ax.set_ylabel('停车需求量（个）')
    ax.set_title('图6：停车需求时段分布图', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图6_停车需求分布.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图6完成")

def generate_chart_7():
    """图7：管理成效对比"""
    print("生成图7...")
    
    categories = ['交通效率', '服务水平', '安全环保', '综合得分']
    workday = [70.0, 66.5, 67.5, 68.0]
    weekend = [76.0, 74.5, 74.0, 75.0]
    holiday = [82.5, 86.0, 87.5, 85.0]
    
    x = np.arange(len(categories))
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(12, 7))
    
    bars1 = ax.bar(x - width, workday, width, label='工作日', color='lightblue', alpha=0.8)
    bars2 = ax.bar(x, weekend, width, label='周末', color='orange', alpha=0.8)
    bars3 = ax.bar(x + width, holiday, width, label='黄金周', color='lightgreen', alpha=0.8)
    
    # 标注数值
    for bars in [bars1, bars2, bars3]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{height:.1f}', ha='center', va='bottom', fontsize=9)
    
    ax.set_xlabel('评价指标')
    ax.set_ylabel('得分')
    ax.set_title('图7：各指标得分对比图', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    
    output_dir = create_output_dir()
    plt.savefig(f'{output_dir}/图7_成效对比图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("  ✓ 图7完成")

def main():
    """主函数"""
    print("开始生成论文图表...")
    print("="*40)
    
    try:
        generate_chart_1()
        generate_chart_2()
        generate_chart_3()
        generate_chart_4()
        generate_chart_5()
        generate_chart_6()
        generate_chart_7()
        
        print("="*40)
        print("✓ 所有图表生成完成！")
        print("✓ 输出目录: 论文图表/")
        print("✓ 图表总数: 7个")
        
    except Exception as e:
        print(f"生成出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
