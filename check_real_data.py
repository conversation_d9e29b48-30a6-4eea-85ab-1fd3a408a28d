#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查原始数据的真实情况
"""

import pandas as pd
import os
from collections import defaultdict

def check_csv_file():
    """检查CSV文件的真实情况"""
    print("="*60)
    print("检查原始数据文件")
    print("="*60)
    
    csv_file = '附件2.csv'
    
    if not os.path.exists(csv_file):
        print("❌ 附件2.csv 文件不存在")
        return None
    
    # 检查文件大小
    file_size = os.path.getsize(csv_file)
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
    
    # 尝试不同编码读取
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
    
    for encoding in encodings:
        try:
            print(f"\n尝试编码: {encoding}")
            
            # 读取前几行检查
            with open(csv_file, 'r', encoding=encoding) as f:
                lines = []
                for i, line in enumerate(f):
                    lines.append(line.strip())
                    if i >= 10:
                        break
            
            print("前10行内容:")
            for i, line in enumerate(lines):
                print(f"  {i+1}: {line}")
            
            # 计算总行数
            with open(csv_file, 'r', encoding=encoding) as f:
                total_lines = sum(1 for line in f)
            
            print(f"总行数: {total_lines:,}")
            print(f"数据行数: {total_lines-1:,} (减去标题行)")
            
            # 如果成功读取，尝试用pandas分析
            try:
                # 读取少量数据进行分析
                df_sample = pd.read_csv(csv_file, encoding=encoding, nrows=1000)
                print(f"\nPandas读取成功:")
                print(f"列名: {list(df_sample.columns)}")
                print(f"数据类型: {df_sample.dtypes.to_dict()}")
                print(f"样本数据形状: {df_sample.shape}")
                
                # 显示样本数据
                print("\n前5行数据:")
                print(df_sample.head().to_string())
                
                return {
                    'encoding': encoding,
                    'total_lines': total_lines,
                    'data_rows': total_lines - 1,
                    'columns': list(df_sample.columns),
                    'sample_data': df_sample
                }
                
            except Exception as e:
                print(f"Pandas读取失败: {e}")
                continue
                
        except Exception as e:
            print(f"编码 {encoding} 失败: {e}")
            continue
    
    print("❌ 所有编码都失败")
    return None

def analyze_data_distribution(data_info):
    """分析数据分布"""
    if not data_info:
        return
    
    print("\n" + "="*60)
    print("数据分布分析")
    print("="*60)
    
    try:
        # 读取完整数据
        df = pd.read_csv('附件2.csv', encoding=data_info['encoding'])
        
        print(f"完整数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 分析各列的分布
        for col in df.columns:
            print(f"\n{col} 列分析:")
            if df[col].dtype == 'object':
                value_counts = df[col].value_counts()
                print(f"  唯一值数量: {df[col].nunique()}")
                print(f"  前5个值: {value_counts.head().to_dict()}")
            else:
                print(f"  数值范围: {df[col].min()} - {df[col].max()}")
                print(f"  平均值: {df[col].mean():.2f}")
        
        # 时间分析
        if '时间' in df.columns or any('时间' in str(col) for col in df.columns):
            time_col = None
            for col in df.columns:
                if '时间' in str(col) or 'time' in str(col).lower():
                    time_col = col
                    break
            
            if time_col:
                print(f"\n时间列 ({time_col}) 分析:")
                try:
                    df[time_col] = pd.to_datetime(df[time_col])
                    print(f"  时间范围: {df[time_col].min()} 至 {df[time_col].max()}")
                    print(f"  时间跨度: {(df[time_col].max() - df[time_col].min()).days} 天")
                    
                    # 按日期统计
                    daily_counts = df[time_col].dt.date.value_counts().sort_index()
                    print(f"  每日数据量: {daily_counts.head().to_dict()}")
                    
                except Exception as e:
                    print(f"  时间解析失败: {e}")
        
        return df
        
    except Exception as e:
        print(f"数据分析失败: {e}")
        return None

def create_realistic_data_plan(df):
    """基于真实数据创建处理方案"""
    if df is None:
        return
    
    print("\n" + "="*60)
    print("数据处理方案规划")
    print("="*60)
    
    data_size = len(df)
    print(f"实际数据量: {data_size:,} 条")
    
    if data_size < 50000:
        # 小数据集处理方案
        print("数据规模: 小型数据集 (< 5万条)")
        print("建议处理方案:")
        print("1. 不需要分割，可以整体处理")
        print("2. 直接生成单个Excel文件")
        print("3. 重点关注数据质量和分析深度")
        
        plan = {
            'split_needed': False,
            'excel_files': 1,
            'processing_strategy': 'single_file',
            'focus': 'quality_analysis'
        }
        
    elif data_size < 500000:
        # 中型数据集处理方案
        print("数据规模: 中型数据集 (5-50万条)")
        print("建议处理方案:")
        print("1. 分割为2-3个文件便于处理")
        print("2. 生成多个Excel文件")
        print("3. 平衡处理效率和分析深度")
        
        plan = {
            'split_needed': True,
            'excel_files': 3,
            'processing_strategy': 'moderate_split',
            'focus': 'balanced_analysis'
        }
        
    else:
        # 大型数据集处理方案
        print("数据规模: 大型数据集 (> 50万条)")
        print("建议处理方案:")
        print("1. 分割为5-10个文件")
        print("2. 采用分批处理策略")
        print("3. 重点关注处理效率")
        
        plan = {
            'split_needed': True,
            'excel_files': min(10, max(5, data_size // 100000)),
            'processing_strategy': 'batch_processing',
            'focus': 'efficiency'
        }
    
    # 数据质量评估
    print(f"\n数据质量评估:")
    missing_data = df.isnull().sum().sum()
    duplicate_data = df.duplicated().sum()
    
    print(f"- 缺失值: {missing_data} 个")
    print(f"- 重复行: {duplicate_data} 个")
    print(f"- 数据完整性: {(1 - missing_data/df.size)*100:.1f}%")
    
    plan['data_quality'] = {
        'missing_values': missing_data,
        'duplicates': duplicate_data,
        'completeness': (1 - missing_data/df.size)*100
    }
    
    return plan

def main():
    """主函数"""
    print("2024年数模大赛E题 - 真实数据检查")
    
    # 1. 检查CSV文件
    data_info = check_csv_file()
    
    if not data_info:
        print("❌ 无法读取数据文件")
        return
    
    # 2. 分析数据分布
    df = analyze_data_distribution(data_info)
    
    # 3. 创建处理方案
    plan = create_realistic_data_plan(df)
    
    if plan:
        print(f"\n" + "="*60)
        print("推荐处理方案总结")
        print("="*60)
        print(f"✓ 数据编码: {data_info['encoding']}")
        print(f"✓ 数据量: {data_info['data_rows']:,} 条")
        print(f"✓ 是否分割: {'是' if plan['split_needed'] else '否'}")
        print(f"✓ Excel文件数: {plan['excel_files']} 个")
        print(f"✓ 处理策略: {plan['processing_strategy']}")
        print(f"✓ 关注重点: {plan['focus']}")
        print(f"✓ 数据完整性: {plan['data_quality']['completeness']:.1f}%")

if __name__ == "__main__":
    main()
