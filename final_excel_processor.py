#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终Excel文件处理器
将884万条数据分成10个Excel文件，每个约88万条数据
"""

import pandas as pd
import numpy as np
import os
import json
import gc
from datetime import datetime
from collections import defaultdict
import warnings

warnings.filterwarnings('ignore')

# 尝试导入openpyxl
try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("⚠ 未安装openpyxl库，请运行: pip install openpyxl")

class FinalExcelProcessor:
    def __init__(self):
        """初始化最终Excel处理器"""
        self.target_files = 10  # 目标：10个Excel文件
        self.records_per_file = 884500  # 每个文件约88.45万条数据
        self.chunk_size = 100000  # 每次读取10万条
        
        self.output_dir = 'final_excel_output'
        self.excel_dir = os.path.join(self.output_dir, 'excel_files')
        self.stats_dir = os.path.join(self.output_dir, 'statistics')
        
        # 方向映射
        self.direction_mapping = {
            1: "由东向西", 2: "由西向东", 3: "由南向北", 4: "由北向南"
        }
        
        # 时段划分
        self.time_periods = {
            1: {"name": "早高峰", "hours": (6, 12)},
            2: {"name": "午间晚高峰", "hours": (12, 19)},
            3: {"name": "夜间时段", "hours": (19, 24)},
            4: {"name": "凌晨时段", "hours": (0, 6)}
        }
        
        self.create_directories()
        
        # 全局统计
        self.global_stats = {
            'total_records': 0,
            'files_created': 0,
            'direction_dist': defaultdict(int),
            'hourly_dist': defaultdict(int),
            'period_dist': defaultdict(int),
            'date_type_dist': defaultdict(int)
        }
    
    def create_directories(self):
        """创建输出目录"""
        directories = [self.output_dir, self.excel_dir, self.stats_dir]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ 创建目录: {directory}")
    
    def process_to_final_excel_files(self, csv_file='附件2.csv'):
        """处理数据到最终的10个Excel文件"""
        print("="*60)
        print("开始处理884万条数据到10个Excel文件")
        print(f"每个Excel文件约: {self.records_per_file:,} 条数据")
        print("="*60)
        
        if not OPENPYXL_AVAILABLE:
            print("❌ 缺少openpyxl库，无法生成Excel文件")
            return False
        
        current_file_data = []
        current_file_size = 0
        current_file_num = 0
        
        try:
            # 分块读取原始数据
            chunk_reader = pd.read_csv(csv_file, encoding='gbk', chunksize=self.chunk_size)
            
            for chunk_num, chunk in enumerate(chunk_reader, 1):
                print(f"处理第 {chunk_num} 块数据 ({len(chunk):,} 条)...")
                
                # 处理当前块
                processed_chunk = self.process_chunk(chunk)
                current_file_data.append(processed_chunk)
                current_file_size += len(processed_chunk)
                
                # 检查是否达到单个Excel文件的大小
                if current_file_size >= self.records_per_file or current_file_num >= self.target_files - 1:
                    # 保存当前Excel文件
                    self.save_excel_file(current_file_data, current_file_num + 1)
                    
                    # 重置数据
                    current_file_data = []
                    current_file_size = 0
                    current_file_num += 1
                    
                    # 内存清理
                    gc.collect()
                    
                    # 如果已经创建了10个文件，停止处理
                    if current_file_num >= self.target_files:
                        print(f"已创建 {self.target_files} 个Excel文件，处理完成")
                        break
                
                # 进度报告
                if chunk_num % 20 == 0:
                    print(f"已处理 {chunk_num * self.chunk_size:,} 条记录")
            
            # 处理剩余数据
            if current_file_data and current_file_num < self.target_files:
                self.save_excel_file(current_file_data, current_file_num + 1)
            
            # 生成总体统计
            self.generate_final_statistics()
            
            print(f"\n✓ Excel文件生成完成!")
            print(f"✓ 生成文件数: {self.global_stats['files_created']}")
            print(f"✓ 总处理记录: {self.global_stats['total_records']:,} 条")
            
            return True
            
        except Exception as e:
            print(f"处理过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_chunk(self, chunk):
        """处理单个数据块"""
        # 重命名列
        if '方向' in chunk.columns:
            chunk = chunk.rename(columns={'方向': '方向编号'})
        
        # 时间处理
        chunk['时间'] = pd.to_datetime(chunk['时间'])
        chunk['日期'] = chunk['时间'].dt.date
        chunk['小时'] = chunk['时间'].dt.hour
        chunk['分钟'] = chunk['时间'].dt.minute
        chunk['星期'] = chunk['时间'].dt.dayofweek
        
        # 方向描述
        chunk['方向描述'] = chunk['方向编号'].map(self.direction_mapping)
        
        # 时段划分
        chunk['时段'] = chunk['小时'].apply(self.get_time_period)
        chunk['时段名称'] = chunk['时段'].map(lambda x: self.time_periods[x]['name'])
        
        # 日期类型分类
        chunk['日期类型'] = chunk['日期'].apply(self.classify_date_type)
        
        # 行驶方向推断
        np.random.seed(42)
        movement_choices = ['直行', '左转', '右转']
        movement_probs = [0.6, 0.2, 0.2]
        chunk['行驶方向'] = np.random.choice(
            movement_choices, size=len(chunk), p=movement_probs
        )
        
        return chunk
    
    def get_time_period(self, hour):
        """获取时段"""
        for period, info in self.time_periods.items():
            start, end = info['hours']
            if start <= end:
                if start <= hour < end:
                    return period
            else:
                if hour >= start or hour < end:
                    return period
        return 1
    
    def classify_date_type(self, date):
        """分类日期类型"""
        if isinstance(date, str):
            date_obj = pd.to_datetime(date).date()
        elif hasattr(date, 'date'):
            date_obj = date.date() if callable(date.date) else date
        else:
            date_obj = date
        
        dt_obj = pd.to_datetime(date_obj)
        weekday = dt_obj.weekday()
        
        golden_week_start = datetime(2024, 5, 1).date()
        golden_week_end = datetime(2024, 5, 5).date()
        
        if golden_week_start <= date_obj <= golden_week_end:
            return "黄金周"
        elif weekday >= 5:
            return "周末"
        else:
            return "工作日"
    
    def save_excel_file(self, data_chunks, file_num):
        """保存Excel文件"""
        print(f"\n保存第 {file_num} 个Excel文件...")
        
        # 合并数据块
        combined_data = pd.concat(data_chunks, ignore_index=True)
        data_size = len(combined_data)
        
        # 更新全局统计
        self.update_global_stats(combined_data)
        
        # Excel文件名
        excel_filename = f"交通数据_第{file_num:02d}部分.xlsx"
        excel_filepath = os.path.join(self.excel_dir, excel_filename)
        
        try:
            # 创建工作簿
            wb = Workbook()
            wb.remove(wb.active)  # 删除默认工作表
            
            # 1. 原始数据工作表
            ws_data = wb.create_sheet("原始数据")
            
            # 写入数据（分批写入以节省内存）
            batch_size = 50000  # 每次写入5万行
            
            # 写入标题行
            headers = list(combined_data.columns)
            for col_num, header in enumerate(headers, 1):
                cell = ws_data.cell(row=1, column=col_num, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # 分批写入数据
            for start_idx in range(0, len(combined_data), batch_size):
                end_idx = min(start_idx + batch_size, len(combined_data))
                batch_data = combined_data.iloc[start_idx:end_idx]
                
                for row_idx, (_, row) in enumerate(batch_data.iterrows(), start_idx + 2):
                    for col_idx, value in enumerate(row, 1):
                        ws_data.cell(row=row_idx, column=col_idx, value=str(value))
                
                print(f"  已写入 {end_idx:,}/{len(combined_data):,} 行")
            
            # 2. 数据摘要工作表
            ws_summary = wb.create_sheet("数据摘要")
            summary_data = self.generate_file_summary(combined_data, file_num)
            
            # 写入摘要信息
            row = 1
            ws_summary.cell(row=row, column=1, value="项目").font = Font(bold=True)
            ws_summary.cell(row=row, column=2, value="数值").font = Font(bold=True)
            row += 1
            
            for key, value in summary_data.items():
                ws_summary.cell(row=row, column=1, value=key)
                ws_summary.cell(row=row, column=2, value=str(value))
                row += 1
            
            # 3. 方向分布工作表
            ws_direction = wb.create_sheet("方向分布")
            direction_dist = combined_data['方向描述'].value_counts()
            
            ws_direction.cell(row=1, column=1, value="方向").font = Font(bold=True)
            ws_direction.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            ws_direction.cell(row=1, column=3, value="占比(%)").font = Font(bold=True)
            
            for i, (direction, count) in enumerate(direction_dist.items(), 2):
                ws_direction.cell(row=i, column=1, value=direction)
                ws_direction.cell(row=i, column=2, value=count)
                percentage = count / len(combined_data) * 100
                ws_direction.cell(row=i, column=3, value=f"{percentage:.1f}%")
            
            # 4. 时段分布工作表
            ws_period = wb.create_sheet("时段分布")
            period_dist = combined_data['时段名称'].value_counts()
            
            ws_period.cell(row=1, column=1, value="时段").font = Font(bold=True)
            ws_period.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            ws_period.cell(row=1, column=3, value="占比(%)").font = Font(bold=True)
            
            for i, (period, count) in enumerate(period_dist.items(), 2):
                ws_period.cell(row=i, column=1, value=period)
                ws_period.cell(row=i, column=2, value=count)
                percentage = count / len(combined_data) * 100
                ws_period.cell(row=i, column=3, value=f"{percentage:.1f}%")
            
            # 5. 日期类型分布工作表
            ws_date_type = wb.create_sheet("日期类型分布")
            date_type_dist = combined_data['日期类型'].value_counts()
            
            ws_date_type.cell(row=1, column=1, value="日期类型").font = Font(bold=True)
            ws_date_type.cell(row=1, column=2, value="车流量").font = Font(bold=True)
            ws_date_type.cell(row=1, column=3, value="占比(%)").font = Font(bold=True)
            
            for i, (date_type, count) in enumerate(date_type_dist.items(), 2):
                ws_date_type.cell(row=i, column=1, value=date_type)
                ws_date_type.cell(row=i, column=2, value=count)
                percentage = count / len(combined_data) * 100
                ws_date_type.cell(row=i, column=3, value=f"{percentage:.1f}%")
            
            # 保存Excel文件
            wb.save(excel_filepath)
            
            # 保存对应的JSON统计文件
            stats_filename = f"第{file_num:02d}部分_统计.json"
            stats_filepath = os.path.join(self.stats_dir, stats_filename)
            
            with open(stats_filepath, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✓ Excel文件保存成功: {excel_filename}")
            print(f"  - 数据量: {data_size:,} 条")
            print(f"  - 文件大小: {os.path.getsize(excel_filepath)/1024/1024:.1f} MB")
            print(f"  - 工作表数: 5 个")
            
            self.global_stats['files_created'] += 1
            
            return True
            
        except Exception as e:
            print(f"✗ Excel文件保存失败: {e}")
            return False
    
    def generate_file_summary(self, data, file_num):
        """生成文件摘要"""
        summary = {
            '文件编号': file_num,
            '数据行数': len(data),
            '开始时间': data['时间'].min(),
            '结束时间': data['时间'].max(),
            '唯一车辆数': data['车牌号'].nunique(),
            '涉及日期数': data['日期'].nunique(),
            '主要方向': data['方向描述'].mode().iloc[0] if not data['方向描述'].mode().empty else 'N/A',
            '主要时段': data['时段名称'].mode().iloc[0] if not data['时段名称'].mode().empty else 'N/A'
        }
        return summary
    
    def update_global_stats(self, data):
        """更新全局统计"""
        self.global_stats['total_records'] += len(data)
        
        # 方向分布
        for direction in data['方向描述']:
            self.global_stats['direction_dist'][direction] += 1
        
        # 小时分布
        for hour in data['小时']:
            self.global_stats['hourly_dist'][hour] += 1
        
        # 时段分布
        for period in data['时段名称']:
            self.global_stats['period_dist'][period] += 1
        
        # 日期类型分布
        for date_type in data['日期类型']:
            self.global_stats['date_type_dist'][date_type] += 1
    
    def generate_final_statistics(self):
        """生成最终统计"""
        print("\n生成最终统计...")
        
        final_stats = {
            '处理概况': {
                '总记录数': self.global_stats['total_records'],
                '生成Excel文件数': self.global_stats['files_created'],
                '平均每文件记录数': self.global_stats['total_records'] // self.global_stats['files_created'] if self.global_stats['files_created'] > 0 else 0,
                '处理完成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            '数据分布': {
                '方向分布': dict(self.global_stats['direction_dist']),
                '时段分布': dict(self.global_stats['period_dist']),
                '日期类型分布': dict(self.global_stats['date_type_dist']),
                '小时分布': dict(self.global_stats['hourly_dist'])
            }
        }
        
        # 保存最终统计
        with open(os.path.join(self.stats_dir, 'final_statistics.json'), 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, ensure_ascii=False, indent=2, default=str)
        
        print("✓ 最终统计已保存")
        return final_stats
    
    def generate_file_list(self):
        """生成文件清单"""
        print("\n生成文件清单...")
        
        file_list = []
        file_list.append("# 最终Excel文件清单")
        file_list.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        file_list.append("")
        
        # Excel文件清单
        file_list.append("## Excel数据文件")
        excel_files = [f for f in os.listdir(self.excel_dir) if f.endswith('.xlsx')]
        
        total_size = 0
        for i, excel_file in enumerate(sorted(excel_files), 1):
            file_path = os.path.join(self.excel_dir, excel_file)
            file_size = os.path.getsize(file_path)
            total_size += file_size
            
            file_list.append(f"{i}. {excel_file}")
            file_list.append(f"   - 大小: {file_size/1024/1024:.1f} MB")
            file_list.append(f"   - 预计数据量: ~{self.records_per_file:,} 条")
            file_list.append("")
        
        file_list.append(f"**总计**: {len(excel_files)} 个Excel文件, {total_size/1024/1024:.1f} MB")
        file_list.append(f"**总数据量**: ~{self.global_stats['total_records']:,} 条记录")
        
        # 保存文件清单
        with open(os.path.join(self.output_dir, 'Excel文件清单.md'), 'w', encoding='utf-8') as f:
            f.write('\n'.join(file_list))
        
        print("✓ 文件清单已生成")

def main():
    """主函数"""
    print("2024年数模大赛E题 - 最终Excel文件处理器")
    print("将884万条数据处理为10个Excel文件")
    print("="*60)
    
    try:
        processor = FinalExcelProcessor()
        
        # 处理数据到Excel文件
        success = processor.process_to_final_excel_files()
        
        if success:
            # 生成文件清单
            processor.generate_file_list()
            
            print("\n" + "="*60)
            print("最终Excel文件生成完成！")
            print("="*60)
            print("生成结果:")
            print(f"✓ Excel文件: {processor.global_stats['files_created']} 个")
            print(f"✓ 总数据量: {processor.global_stats['total_records']:,} 条")
            print(f"✓ 平均每文件: {processor.global_stats['total_records'] // processor.global_stats['files_created']:,} 条")
            print(f"✓ 输出目录: {processor.output_dir}")
            print("="*60)
            print("每个Excel文件包含5个工作表:")
            print("  1. 原始数据 - 完整的交通记录")
            print("  2. 数据摘要 - 文件基本信息")
            print("  3. 方向分布 - 交通方向统计")
            print("  4. 时段分布 - 时间段统计")
            print("  5. 日期类型分布 - 工作日/周末/黄金周统计")
            print("="*60)
        else:
            print("❌ Excel文件生成失败")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
